import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzSwitchModule } from 'ng-zorro-antd/switch';

import { NzInputModule } from 'ng-zorro-antd/input';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTableModule } from 'ng-zorro-antd/table';
import { ModifyHistoryModule } from '../../../bussiness-components/modify-history/modify-history.module';
import { ExtendEditorModalModule } from '../../../bussiness-components/extend-editor-modal/extend-editor-modal.module';
import { DslWorkDesignModule } from '../../components/dsl-work-design/dsl-work-design.module';
import { PreviewModalModule } from '../../../bussiness-components/preview-modal/preview-modal.module';
import { InputModule } from 'components/form-components/input/input.module';
import { PublishButtonModule } from '../../../bussiness-components/module-publish-button/module-publish-button.module';

import { DataEntryWorkDesignComponent } from './data-entry-work-design.component';
import { StateManagementComponent } from './components/state-management/state-management.component';
import { PageInfoModalComponent } from './components/page-info-modal/page-info-modal.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { DirectiveModule } from '../../../../common/directive/directive.module';
// import { OpenwindowWorkDesignModule } from '../openwindow-work-design/openwindow-work-design.module';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { AdEmptyModule } from 'components/ad-ui-components/ad-empty/ad-empty.module';
import { NzDividerModule } from 'ng-zorro-antd/divider';

import { BusinessShareConsumerModule } from '../../../bussiness-components/business-share-consumer/business-share-consumer.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { NzFormModule } from 'ng-zorro-antd/form';
// import { CustomWidgetModule } from 'components/bussiness-components/custom-widget/custom-widget.module';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { CustomPrintModule } from 'components/bussiness-components/custom-print/custom-print.module';
import { OperatePermissionManagementModule } from 'components/page-design/entries/data-entry-work-design/components/operate-permission-management/operate-permission-management.module';
import { UpdateSubpageModalComponent } from './components/update-subpage-modal/update-subpage-modal.component';
import { AgGridModule } from 'ag-grid-angular';
import {LibPageDesignModule} from "../lib-page-design/lib-page-design.module";

@NgModule({
  declarations: [
    DataEntryWorkDesignComponent,
    StateManagementComponent,
    PageInfoModalComponent,
    UpdateSubpageModalComponent,
  ],
  exports: [DataEntryWorkDesignComponent],
  imports: [
    CommonModule,
    NzSpinModule,
    NzToolTipModule,
    AdButtonModule,
    TranslateModule,
    AdIconModule,
    NzSwitchModule,

    NzInputModule,
    NzCheckboxModule,
    AdSelectModule,
    AdEmptyModule,
    NzTableModule,
    NzDropDownModule,
    FormsModule,
    ReactiveFormsModule,
    ModifyHistoryModule,
    InputModule,
    ExtendEditorModalModule,
    DslWorkDesignModule,
    PreviewModalModule,
    PublishButtonModule,
    DirectiveModule,
    BusinessShareConsumerModule,
    // OpenwindowWorkDesignModule,

    NzDividerModule,
    AdModalModule,
    NzFormModule,
    // CustomWidgetModule,
    NzUploadModule,
    CustomPrintModule,
    OperatePermissionManagementModule,
    AgGridModule,
    LibPageDesignModule,
  ],
})
export class DataEntryWorkDesignModule {}
