import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ModelValidators } from '../../utils/model-validators';
import { DataEntryWorkDesignRequestService } from '../../service/data-entry-work-design-request.service';
import { DataEntryWorkDesignService } from '../../service/data-entry-work-design.service';
import { AppService } from 'pages/apps/app.service';
import { AdModalService } from '../../../../../ad-ui-components/ad-modal/ad-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { updatePageUIElementData } from '../operate-permission-management/operate-permission-management.tool';

export interface PageEditInfo {
  data: any;
  type: 'add' | 'editSubPage' | 'editIsCustom' | 'editPermissionOnly';
}

@Component({
  selector: 'app-page-info-modal',
  templateUrl: './page-info-modal.component.html',
  styleUrls: ['./page-info-modal.component.less'],
  providers: [DataEntryWorkDesignRequestService],
})
export class PageInfoModalComponent implements OnInit, OnDestroy {
  @Input() infoModalVisible: boolean = false;
  @Input() editInfo: PageEditInfo;
  @Output() onSubmit: EventEmitter<any> = new EventEmitter();
  @Output() onClose: EventEmitter<any> = new EventEmitter();

  dataForm: FormGroup;
  formLang: any;

  get appCode(): string {
    return this.appService.selectedApp?.code;
  }

  constructor(
    private fb: FormBuilder,
    public service: DataEntryWorkDesignService,
    private appService: AppService,
    private modal: AdModalService,
    private translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.handleReset();
  }

  ngAfterViewInit(): void {
    this.handleInit(this.editInfo);
  }

  handleInit(editInfo: PageEditInfo): void {
    if (!editInfo || !this.infoModalVisible) return;
    const { type, data } = editInfo;
    switch (type) {
      case 'editIsCustom':
        this.dataForm.reset({
          isCustom: [false],
        });
        break;
      case 'editPermissionOnly':
        break;
      default:
        this.dataForm.reset({
          name: [null, [ModelValidators.trim, Validators.required]],
        });
        break;
    }

    this.dataForm?.patchValue(data);
    this.formLang = data?.lang ?? {};
  }

  handleCancel(): void {
    this.handleReset();
    this.onClose.emit();
  }

  async handleConfirm(): Promise<void> {
    for (const i of Object.keys(this.dataForm.controls)) {
      this.dataForm.controls[i].markAsDirty();
      this.dataForm.controls[i].updateValueAndValidity();
    }
    if (this.dataForm.valid) {
      const returnEditInfo: PageEditInfo = {
        type: this.editInfo.type,
        data: {},
      };

      const { name, isCustom } = this.dataForm.getRawValue();

      switch (this.editInfo.type) {
        case 'add':
          returnEditInfo.data = {
            name,
            lang: this.formLang,
          };
          break;
        case 'editSubPage':
          returnEditInfo.data = {
            code: this.editInfo.data.code,
            lang: this.formLang,
            name,
          };
          break;
        case 'editIsCustom':
          returnEditInfo.data = {
            isCustom,
          };
          break;
        default:
          break;
      }

      // 转定制时提示
      if (this.editInfo.type === 'editIsCustom' && !!this.editInfo.data.isCustom && !isCustom) {
        this.modal.confirm({
          nzTitle: this.translate.instant(
            'dj-转标准后，界面设计中仅支持动态按钮组件设计，原SubmitActions提交按钮相关数据将丢失，建议备份，是否继续？',
          ),
          nzWrapClassName: 'vertical-center-modal',
          nzOkText: this.translate.instant('dj-确定'),
          nzOnOk: () => {
            this.onSubmit.emit(returnEditInfo);
            this.handleReset();
          },
          nzOnCancel: () => {},
        });
      } else {
        this.onSubmit.emit(returnEditInfo);
        this.handleReset();
      }
    }
  }

  handleReset(): void {
    this.dataForm = this.fb.group({
      name: [null, this.editInfo?.type === 'add' ? [ModelValidators.trim, Validators.required] : []],
      isCustom: [false],
    });
    this.formLang = null;
  }

  handlePatchLang(key: any, data: any): void {
    this.dataForm.patchValue({ [key]: data?.value });
    if (data.needLang) {
      this.formLang = {
        ...(this.formLang || {}),
        [key]: data.lang?.value,
      };
    }
  }

  ngOnDestroy(): void {}

  handleChangeOperateCustomTemplate(customs: any[]): void {
    this.service.setOperateCustomTemplateRelates(customs);
  }

  handleChangeOperation(params: any): void {
    const isCustom = this.dataForm?.get('isCustom')?.value;
    // const iamConditions = this.service?.workDesignInfo?.iamCondition ?? [];
    const operateCustomTemplateRelates = this.service.page?.operateCustomTemplateRelates ?? [];
    // const pageCode = this.service?.config.pageCode;
    const { type, data } = params;
    /**
     * 只有定制界面可以删除
     */
    if (type === 'delete') {
      const restTemplates = operateCustomTemplateRelates.filter((item: any) => item.targetId !== data.componentId);
      this.service.setOperateCustomTemplateRelates(restTemplates);
      // if (data?.authKey) {
      //   const newIamConditions = iamConditions.map((condition) => {
      //     if (condition.key === data.authKey) {
      //       return {
      //         ...condition,
      //         refComponents: condition.refComponents.filter(
      //           (item: any) =>
      //             !(item.componentId === data.componentId && item.pageCode === pageCode && item.platform === 'PC'),
      //         ),
      //       };
      //     } else {
      //       return condition;
      //     }
      //   });
      //   this.service.setWorkDesignInfoByKey('iamCondition', newIamConditions);
      // }
      /**
       * 只有定制界面可以新增
       */
    } else if (type === 'add') {
      const { componentId, componentName, lang, ...rest } = data;
      this.service.setOperateCustomTemplateRelates([
        ...operateCustomTemplateRelates,
        {
          ...rest,
          targetId: componentId,
          targetName: componentName,
          lang: {
            targetName: lang?.componentName,
          },
        },
      ]);
      // if (data?.authKey) {
      //   const newIamConditions = iamConditions.map((condition) => {
      //     if (condition.key === data.authKey) {
      //       return {
      //         ...condition,
      //         refComponents: [...(condition.refComponents ?? []), data],
      //       };
      //     } else {
      //       return condition;
      //     }
      //   });
      //   this.service.setWorkDesignInfoByKey('iamCondition', newIamConditions);
      // }
    } else if (type === 'edit') {
      if (isCustom) {
        const { componentId, componentName, lang, ...rest } = data;
        const newTemplates = operateCustomTemplateRelates.map((item: any) => {
          if (item.targetId === data.componentId) {
            return {
              ...rest,
              targetId: componentId,
              targetName: componentName,
              lang: {
                targetName: lang?.componentName,
              },
            };
          }
          return item;
        });
        this.service.setOperateCustomTemplateRelates(newTemplates);
      } else {
        const pageUIElement = this.service.getCurrentDataSourcePageUIElementData();
        const newPageUIElement = updatePageUIElementData(data, pageUIElement);
        this.service.setCurrentDataSourcePageUIElementData(newPageUIElement);
      }
      // const newIamConditions = iamConditions.map((condition) => {
      //   if (condition.key === data.authKey) {
      //     return {
      //       ...condition,
      //       refComponents: [...(condition.refComponents ?? []), data],
      //     };
      //   } else {
      //     return {
      //       ...condition,
      //       refComponents: condition.refComponents?.filter((comp) => comp.componentId !== data.componentId) ?? [],
      //     };
      //   }
      // });
      /**
       * TODO: 暂时通过这种方式去刷新操作列表，需要优化
       */
      const iamConditions = this.service?.workDesignInfo?.iamCondition ?? [];
      this.service.setWorkDesignInfoByKey('iamCondition', [...iamConditions]);
    }
  }

  handleIamConditionChange(iamConditions: any[]): void {
    this.service.setWorkDesignInfoByKey('iamCondition', iamConditions);
  }
}
