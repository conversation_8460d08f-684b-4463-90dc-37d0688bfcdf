<ad-modal
  nzClassName="page-info-modal"
  [nzTitle]="(editInfo?.type !== 'add' ? 'dj-编辑' : 'dj-新增') | translate"
  [nzContent]="modalContent"
  [nzFooter]="null"
  [nzWidth]="'524px'"
  [(nzVisible)]="infoModalVisible"
  [nzMaskClosable]="false"
  [nzClosable]="true"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalContent>
    <form nz-form [formGroup]="dataForm" [nzNoColon]="true">
      <nz-form-item *ngIf="editInfo?.type !== 'editIsCustom' && editInfo?.type !== 'editPermissionOnly'">
        <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
          <div
            nz-tooltip
            [nzTooltipTitle]="'dj-子页面名称' | translate"
            nzTooltipTrigger="click"
            nzTooltipPlacement="bottom"
          >
            <app-component-input
              formControlName="name"
              [attr]="{
                code: 'name',
                needLang: true,
                lang: { value: this.formLang?.name },
                placeholder: 'dj-请输入' | translate,
                maxlength: 50,
                innerLabel: true,
                label: 'dj-名称',
                required: true
              }"
              [value]="this.formLang?.name?.[('dj-LANG' | translate)]"
              ngDefaultControl
              (callBack)="handlePatchLang('name', $event)"
            >
            </app-component-input>
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item *ngIf="editInfo?.type === 'editIsCustom'">
        <nz-form-control>
          <label nz-checkbox formControlName="isCustom">
            {{ 'dj-是否定制' | translate }}
          </label>
        </nz-form-control>
      </nz-form-item>
    </form>
    <app-operate-permission-management
      *ngIf="editInfo?.type === 'editIsCustom' || editInfo?.type === 'editPermissionOnly'"
      [isCustom]="dataForm?.get('isCustom')?.value"
      [appCode]="appCode"
      [operateCustomTemplateRelates]="service.page?.operateCustomTemplateRelates"
      [pageCode]="service?.activePageInfo?.code"
      [iamCondition]="service.workDesignInfo?.iamCondition"
      [code]="service.workDesignInfo?.code"
      [name]="service.workDesignInfo?.name"
      [pageUIElement]="service.getCurrentDataSourcePageUIElementData()"
      (iamConditionChange)="handleIamConditionChange($event)"
      (changeOperation)="handleChangeOperation($event)"
    ></app-operate-permission-management>
    <!-- <app-custom-print -->
    <!--   class="app-custom-print" -->
    <!--   [ngStyle]="{ -->
    <!--     display: dataForm?.get('isCustom')?.value ? 'unset' : 'none' -->
    <!--   }" -->
    <!--   [fields]="[]" -->
    <!--   [appCode]="appCode" -->
    <!--   [operations]="service.operateCustomTemplateRelates" -->
    <!--   [pageCode]="service.config.pageCode" -->
    <!--   (changeOperateCustomTemplate)="handleChangeOperateCustomTemplate($event)" -->
    <!-- > -->
    <!-- </app-custom-print> -->
    <div class="modal-footer">
      <button ad-button adType="default" (click)="handleCancel()">
        {{ 'dj-取消' | translate }}
      </button>
      <ng-container *operateAuth="{ prefix: 'update' }">
        <button ad-button adType="primary" (click)="handleConfirm()">
          {{ 'dj-确定' | translate }}
        </button>
      </ng-container>
    </div>
  </ng-template>
</ad-modal>
