
  <ad-empty class="work-design-empty" [nzNotFoundContent]="contentTpl" *ngIf="!!gobalErrorMessage">
    <ng-template #contentTpl>
      <span>{{ gobalErrorMessage }}</span>
    </ng-template>
  </ad-empty>
  <div class="work-design" *ngIf="!gobalErrorMessage">
    <div class="work-header">
      <div [class]="modelPageType !== 'notModelDriven' ? 'header-title-tpl' : 'header-title'">
        <div
          *ngIf="modelPageType === 'notModelDriven'; else headerCustom"
          nz-tooltip
          [nzTooltipTitle]="dataEntryWorkDesignService.workDesignInfo?.name"
        >
          {{ dataEntryWorkDesignService.workDesignInfo?.name }}
        </div>
        <ng-template #headerCustom [ngTemplateOutlet]="headerCustomTemplate"></ng-template>
      </div>
      <div class="header-menu">
        <a
          class="header-menu-show"
          nz-dropdown
          [nzDropdownMenu]="menuTab"
          [nzOverlayClassName]="'work-design'"
          [nzTrigger]="'click'"
          (nzVisibleChange)="handlePageSelectVisibleChange($event)"
        >
          {{ dataEntryWorkDesignService?.activePageInfo?.name }}
          <i [class]="{ open: isPageSelectVisible }" adIcon iconfont="iconzhankai1" aria-hidden="true"></i>
        </a>

        <nz-dropdown-menu #menuTab="nzDropdownMenu">
          <ul class="header-menu-list" nz-menu>
            <li
              nz-menu-item
              class="header-menu-list-item"
              [class]="{
                active: page === dataEntryWorkDesignService?.activePageInfo
              }"
              *ngFor="let page of dataEntryWorkDesignService.menuPageInfoList"
              (click)="handleTogglePage(page)"
            >
              <span class="text-overflow" (click)="handleTogglePage(page)"> {{ page.name }}</span>
              <span class="main-page" *ngIf="getIsMainPage(page)">{{ 'dj-主页面' | translate }}</span>
              <!-- <i adIcon iconfont="iconzhu" class="main-page" *ngIf="getIsMainPage(page)" aria-hidden="true"></i> -->
            </li>
            <nz-divider></nz-divider>
            <li
              nz-menu-item
              class="header-menu-list-item"
              [class]="{
                active: page === dataEntryWorkDesignService?.activePageInfo
              }"
              *ngFor="let page of dataEntryWorkDesignService.subPageInfoList"
            >
              <span class="item-handle-title text-overflow" (click)="handleTogglePage(page)"> {{ page.name }}</span>
              <div class="item-handle-button" nz-tooltip [nzTooltipTitle]="'dj-同步页面' | translate">
                <i adIcon iconfont="icontongbuyeqian" aria-hidden="true" (click)="handleUpdateSubPage(page.code)"></i>
              </div>
              <div class="item-handle-button" nz-tooltip [nzTooltipTitle]="'dj-删除' | translate">
                <i adIcon iconfont="iconshanchu3" aria-hidden="true" (click)="handleDeleteSubPage(page.code)"></i>
              </div>
            </li>

            <li nz-menu-item class="header-menu-list-item handle-button" (click)="handleAddSubPage()">
              <i adIcon iconfont="iconzengjia" aria-hidden="true"></i> {{ 'dj-添加子页面' | translate }}
            </li>
          </ul>
        </nz-dropdown-menu>
      </div>
      <div class="menu-divider"></div>
      <div class="menu-toolbar">
        <!-- <ng-container *operateAuth="{ prefix: 'update' }">
          <div class="menu-toolbar-item" *ngIf="isShowQueryPlanEditButton" (click)="handleCurrentPageQueryPlan()">
            <i adIcon iconfont="iconjiedianshezhi" aria-hidden="true"></i>
            <span>{{ 'dj-页面设置' | translate }}</span>
          </div>
        </ng-container> -->
        <!-- <ng-container *operateAuth="{ prefix: 'update' }"> -->
        <div class="menu-toolbar-item" (click)="handleEditCurrentPage()">
          <i adIcon iconfont="iconjiedianshezhi" aria-hidden="true"></i>
          <span>{{ 'dj-页面设置' | translate }}</span>
        </div>
        <!-- </ng-container> -->
        <!-- <ng-container *operateAuth="{ prefix: 'update' }"> -->
        <div class="menu-toolbar-item" *ngIf="showStateModalButton" (click)="openStateModal()">
          <i adIcon iconfont="iconxiangmuka" aria-hidden="true"></i>
          <span>{{ 'dj-状态管理' | translate }}</span>
        </div>
        <!-- </ng-container> -->
        <!-- <ng-container *operateAuth="{ prefix: 'update' }"> -->
        <div class="menu-toolbar-item" (click)="openCodeModal()">
          <i adIcon iconfont="icondaima123" aria-hidden="true"></i>
          <span>{{ 'dj-代码' | translate }}</span>
        </div>
        <!-- </ng-container> -->
        <ng-container *operateAuth="{ prefix: 'update' }">
          <div class="menu-toolbar-item" *ngIf="showPreviewButton" (click)="handlePreview(true)">
            <i adIcon iconfont="iconyulan" aria-hidden="true"></i>
            <span>{{ 'dj-预览' | translate }}</span>
          </div>
        </ng-container>
      </div>
      <div class="header-btn" [ngStyle]="{ 'padding-right': modelPageType !== 'notModelDriven' ? '20px' : '60px' }">
        <ng-container *operateAuth="{ prefix: 'update' }">
          <app-module-publish-button
            #publishButton
            [module]="'DataEntry'"
            [size]="'default'"
            [pkValue]="dataEntryWorkDesignService.workData.code"
            [needTenant]="true"
            [needSave]="true"
            (clickPublicAction)="handleClickPublicAction()"
            (publicAction)="dataEntryWorkDesignService.setSaveLoading($event)"
          ></app-module-publish-button>
        </ng-container>

        <ng-container *operateAuth="{ prefix: 'update' }">
          <button ad-button adType="primary" (click)="handleSave()">
            {{ 'dj-保存' | translate }}
          </button>
        </ng-container>

        <a nz-dropdown [nzDropdownMenu]="menu">
          <i class="more" adIcon type="ellipsis"></i>
        </a>
        <nz-dropdown-menu #menu="nzDropdownMenu">
          <ul nz-menu nzSelectable>
            <li nz-menu-item (click)="openModifyHistoryModal()">{{ 'dj-修改历史' | translate }}</li>
          </ul>
        </nz-dropdown-menu>
      </div>
    </div>

    <div class="work-body" *ngIf="!dataEntryWorkDesignService.initInfoLoading">
      <!-- [ngClass]="{ hide: dataEntryWorkDesignService.isCustom }" -->
      <!-- <app-business-share-consumer -->
      <!--   *ngIf="!!dynamicWorkDesignBusinessShareInfo" -->
      <!--   #dynamicWorkDesign -->
      <!--   [businessShareInfo]="dynamicWorkDesignBusinessShareInfo" -->
      <!--   [isPreload]="false" -->
      <!--   appCollaborate -->
      <!--   [collaborateInfo]="{ -->
      <!--     type: designerType + modelPageType, -->
      <!--     sourceId: dataEntryWorkDesignService.workDesignInfo?.code -->
      <!--   }" -->
      <!-- ></app-business-share-consumer> -->
      <!--<app-business-share-consumer
        *ngIf="!!dynamicWorkDesignBusinessShareInfo"
        #dynamicWorkDesign
        [businessShareInfo]="dynamicWorkDesignBusinessShareInfo"
        appCollaborate
        [collaborateInfo]="{
          type: designerType + modelPageType,
          sourceId: dataEntryWorkDesignService.workDesignInfo?.code
        }"
      ></app-business-share-consumer>-->

      <app-lib-page-design
        *ngIf="!!dynamicWorkDesignBusinessShareInfo"
        #dynamicWorkDesign
        [dslBusinessData]="dynamicWorkDesignBusinessShareInfo"
      ></app-lib-page-design>

      <!-- <app-custom-widget
        class="custom-widget-container"
        [ngClass]="{ hide: !dataEntryWorkDesignService.isCustom }"
        [codeTip]="dataEntryWorkDesignService.customTip"
        (switchStandard)="handleChangeCustom(false)"
      >
      </app-custom-widget> -->
    </div>
  </div>


<!-- 预览 -->
<app-component-preview-modal
  *ngIf="previewVisible"
  [(visible)]="previewVisible"
  [params]="previewParam"
></app-component-preview-modal>

<!-- 代码 -->
<app-extend-editor-modal
  *ngIf="showCodeModal"
  [data]="codeData"
  [title]="'dj-代码' | translate"
  (ok)="handleCodeModal($event)"
  (close)="showCodeModal = false"
>
</app-extend-editor-modal>

<!-- 历史 -->
<app-modify-history-modal
  *ngIf="historyModalProps.transferModal"
  [historyModalProps]="historyModalProps"
  (closeModal)="historyModalProps.transferModal = false"
>
</app-modify-history-modal>

<!-- 新增/编辑 信息面板 -->
<app-page-info-modal
  *ngIf="isPageInfoModalVisible"
  [infoModalVisible]="isPageInfoModalVisible"
  [editInfo]="pageEditInfo"
  (onSubmit)="handlePageInfoModalSubmit($event)"
  (onClose)="handlePageInfoModalClose()"
></app-page-info-modal>

<app-update-subpage-modal
  [infoModalVisible]="isUpdateSubpageModalVisible"
  [pageInfoList]="updateSubpageModalPageInfoList"
  [pageInfo]="updateSubpageModalPageInfo"
  (onSubmit)="handleUpdateSubpageModalSubmit($event)"
  (onClose)="isUpdateSubpageModalVisible = false"
>
</app-update-subpage-modal>

<!-- <app-query-plan-modal
  [infoModalVisible]="isQueryPlanModalVisible"
  [editInfo]="queryPlanEditInfo"
  (onSubmit)="handleQueryPlanModalSubmit($event)"
  (onClose)="handleQueryPlanModalClose()"
></app-query-plan-modal> -->

<!-- <app-openwindow-work-design
  *ngIf="openwindowVisible"
  [workVisible]="openwindowVisible"
  [workData]="openwindowData"
  (close)="handleCloseOpenwindow()"
  (onOk)="handleSaveOpenwindow($event)"
>
</app-openwindow-work-design> -->

<app-state-management
  *ngIf="showStateModal"
  [extendedFields]="dataEntryWorkDesignService.workDesignInfo?.extendedFields"
  (ok)="handleStateModalOk($event)"
  (cancel)="showStateModal = false"
></app-state-management>
