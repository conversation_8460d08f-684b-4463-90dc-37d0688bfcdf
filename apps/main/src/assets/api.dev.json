{"apiUrl": "https://semcdwscreen-paas.apps.digiwincloud.com.cn", "iamUrl": "https://iam-test.digiwincloud.com.cn", "emcUrl": "https://emc-test.digiwincloud.com.cn", "mscUrl": "https://msc-test.digiwincloud.com.cn", "eocUrl": "https://eoc-test.digiwincloud.com.cn", "dmcUrl": "https://dmc-test.digiwincloud.com.cn", "themeMapUrl": "https://knowledgemaps-paas.apps.digiwincloud.com.cn/restful/service", "smartDataUrl": "", "uibotUrl": "https://uibot-paas.apps.digiwincloud.com.cn", "agileInteractionUrl": "", "atmcUrl": "", "multiTenant": "", "athenaUrl": "https://athena-paas.digiwincloud.com.cn", "consoleUrl": "https://console-test.digiwincloud.com.cn", "muiUrl": "", "bpmUrl": "", "digiwincloudUrl": "", "aimUrl": "", "aamUrl": "", "adesignerUrl": "https://adp-paas.apps.digiwincloud.com.cn", "uiDesignerUrl": "https://athena-ui-designer-paas.digiwincloud.com.cn", "publishUrl": "https://aadc-paas.apps.digiwincloud.com.cn", "clientId": "2a6a5aabe9bd27dc710822764b499443beb562ea49593f8a436893590b3fb447", "appToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6ImF0aGVuYS1sY2RwIiwiaW5zaWRlIjpmYWxzZSwic2lkIjo0NzEzMTUyMTExMzM1MDR9.pT0WT9qO8j9IVxBBO8HkUFRMd9bSfeQOyw0Oh4xL34U", "tenantAppToken": "", "abiReportUrl": "https://digiwinabi-paas.apps.digiwincloud.com.cn", "lcdpDesignerUrl": "https://athena-lcdp-paas.apps.digiwincloud.com.cn", "apiMgmtUrl": "https://apimgmt-paas.apps.digiwincloud.com.cn", "athenaDeployUrl": "https://athena-deployer-paas.digiwincloud.com.cn", "tbbReportUrl": "https://tbb.apps.digiwincloud.com.cn:2447", "tbbLoginUrl": "https://tbb.apps.digiwincloud.com.cn:2446", "kcfUrl": "https://kcq-ai-ddic2023ws.apps.digiwincloud.com", "gmcUrl": "https://gmc-paas.digiwincloud.com.cn", "bossIamUrl": "https://bossiam-test.digiwincloud.com.cn", "athenaConsole": "https://athena-console-paas.digiwincloud.com.cn", "designerUrl": "https://athena-designer-paas.digiwincloud.com.cn", "mobileUrl": "https://athena-mobile-paas.digiwincloud.com.cn", "tddUrl": "https://tdd-paas.apps.digiwincloud.com.cn", "imUrl": "https://im-paas.apps.digiwincloud.com.cn", "tbbUrl": "https://tbb.apps.digiwincloud.com.cn:8105", "biUrl": "https://digiwinabi-paas.apps.digiwincloud.com.cn", "alearningUrl": "https://alearning-paas.apps.digiwincloud.com.cn", "taskEngineUrl": "https://taskengine-paas.apps.digiwincloud.com.cn/restful/standard/taskengine", "nlpImUrl": "https://nlpim-paas.apps.digiwincloud.com.cn", "nlpBotUrl": "https://webot-kf.digiwincloud.com.cn", "itUrl": "https://athena-it-paas.digiwincloud.com.cn", "ocrApiUrl": "https://bai-cloudeia.digiwin.com:8088", "mdcUrl": "https://mdc-paas.apps.digiwincloud.com.cn", "lmcUrl": "https://lmc-test.digiwincloud.com.cn", "ddsmDesignerUrl": "https://eas-designer-paas.digiwincloud.com.cn", "espUrl": "https://esp-paas.apps.digiwincloud.com.cn", "ganttChartServiceUrl": "https://ganttchart-paas.apps.digiwincloud.com.cn", "apaTKUrl": "https://apa-tk-test.apps.digiwincloud.com.cn", "semcUrl": "https://semc-paas.apps.digiwincloud.com.cn", "semcWebUrl": "https://semcweb-paas.digiwincloud.com.cn", "knowledgeMapsUrl": "https://knowledgemaps-paas.apps.digiwincloud.com.cn/restful/service", "developerPortalUrl": "https://dev-test.digiwincloud.com.cn", "atdmUrl": "", "athenaDesignerCoreUrl": "https://adp-paas.apps.digiwincloud.com.cn", "dccUrl": "https://bmd-dmsc-fe-main-paas-paas.digiwincloud.com.cn", "developerUrl": "https://dev-console.digiwincloud.com.cn", "asaDesignerUrl": "https://asa-designer-web-paas.digiwincloud.com.cn", "hooksDemoUrl": "https://z0lxpczot6u.feishu.cn/wiki/W0UhwGq1TiMA2UknG49cA9yLnqe", "enableMqtt": "true", "mqttUrl": "wss://emqx-athena-test.digiwincloud.com.cn", "mqttPort": "8084", "mqttPath": "/mqtt", "mqttUserName": "", "mqttPwd": "", "mqttTopicPrefix": "/ADP/UPGRADE_NOTIFICATION/PAAS", "appCodeEnv": "AT", "envAlias": "PAAS", "experienceServiceCode": "DPBAS", "cloud": "HUAWEI", "platformCategory": "SYSTEM", "tenantDesignerUrl": "https://athena-tenant-dev-platform-paas.digiwincloud.com.cn", "athenaDesignerUrl": "https://athena-dev-platform-paas.digiwincloud.com.cn", "athenaMechanismCoreUrl": "https://athena-mechanism-web-paas.digiwincloud.com.cn", "dataCenterUrl": "https://hw-test-dmp-dmf.digiwincloud.com.cn/sso-login", "devOpsUrl": "https://dev-console-test.digiwincloud.com.cn", "isLandholder": "false", "tbbRenderUrl": "https://tbb.apps.digiwincloud.com.cn:2456", "hiddenMenuByEnv": "", "allowLowcodeTenants": "FIVE,digiwinBmOpt,lcdp,DWappbmc,athenaDev,DWCK0420,lcdpPreTest,IntelligentDriveCenterWorkbench,AthenaAutoTest,athenaPaaSDesigner,AthenaISVTest,AthenaQCTestW,AthenaQCPaasW,AthenaAutoProdHw,AthenaQCProdE,FJBTESTZUHU,TSED,tkwsp,dwfanganzc,AthenaAutoPaas", "nanaUrl": "https://ania-web-paas.digiwincloud.com.cn", "aiEnv": "AliTest-PROD", "scherecom": "https://scherecom-paas.apps.digiwincloud.com.cn", "enableOpenVscode": "true", "showHighCodeByAppTypes": "1_4_5_7", "marketUrl": "https://market.digiwincloud.com.cn"}