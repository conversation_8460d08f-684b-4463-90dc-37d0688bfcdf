{"pageCommonParams": ["proxyPage"], "ComponentCommonHooksMobile": [{"name": "init", "params": ["proxyPage"], "description": "在原生页面或组件的State对象被创建后，会立即调用方法，只会被调用一次。", "lang": {"description": {"zh_CN": "在原生页面或组件的State对象被创建后，会立即调用方法，只会被调用一次。", "zh_TW": "在原生頁面或組件的State對象被創建後，會立即調用方法，只會被調用一次。", "en_US": "After the State object of a native page or component is created, the method will be called immediately and will only be called once."}}}, {"name": "didChangeDependencies", "params": ["proxyPage"], "description": "当State对象的依赖项发生变化时，会触发该方法", "lang": {"description": {"zh_CN": "当State对象的依赖项发生变化时，会触发该方法", "zh_TW": "當State對象的依賴項發生變化時，會觸發該方法", "en_US": "When the dependency of the State object changes, this method is triggered"}}}, {"name": "didUpdateWidget", "params": ["proxyPage"], "description": "该Widget的配置发生了变化，那么会调用方法", "lang": {"description": {"zh_CN": "该Widget的配置发生了变化，那么会调用方法", "zh_TW": "該Widget的配寘發生了變化，那麼會調用方法", "en_US": "The configuration of this widget has changed, so the method will be called"}}}, {"name": "deactivate", "params": ["proxyPage"], "description": "当该State从树中被移除并暂时失去上下文时，会调用该方法", "lang": {"description": {"zh_CN": "当该State从树中被移除并暂时失去上下文时，会调用该方法", "zh_TW": "當該State從樹中被移除並暫時失去上下文時，會調用該方法", "en_US": "When the State is removed from the tree and temporarily loses its context, this method is called"}}}, {"name": "dispose", "params": ["proxyPage"], "description": "当State对象永久从树中被移除时，会调用该方法", "lang": {"description": {"zh_CN": "当State对象永久从树中被移除时，会调用该方法", "zh_TW": "當State對象永久從樹中被移除時，會調用該方法", "en_US": "When the State object is permanently removed from the tree, this method is called"}}}], "PageTypeHooksMobile": [{"name": "pause", "params": ["proxyPage"], "description": "当跳转到其他页面，当前页面处于Pause状态，调用该方法", "lang": {"description": {"zh_CN": "当跳转到其他页面，当前页面处于Pause状态，调用该方法", "zh_TW": "當跳轉到其他頁面，當前頁面處於Pause狀態，調用該方法", "en_US": "When redirecting to another page and the current page is in the Pause state, call this method"}}}, {"name": "resume", "params": ["proxyPage"], "description": "当从其他页面返回该页面，当前页面处于resume状态，调用该方法", "lang": {"description": {"zh_CN": "当从其他页面返回该页面，当前页面处于resume状态，调用该方法", "zh_TW": "當從其他頁面返回該頁面，當前頁面處於resume狀態，調用該方法", "en_US": "When returning from another page and the current page is in the resume state, call this method"}}}], "PrivateComponentCustomHookParams": ["proxyPage", "curComponent"], "InputTypeOnChangeHook": {"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, "InputTypeOnCompleteHook": {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}, "LayoutTypeOnTapHook": {"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "用户单击产生的事件", "lang": {"description": {"zh_CN": "用户单击产生的事件", "zh_TW": "用戶按一下產生的事件", "en_US": "Events generated by user clicks"}}}, "LayoutTypeOnTabChangeHook": {"name": "onTabChange", "params": ["proxyPage", "curComponent"], "description": "不同TAB页面切换时触发该事件", "lang": {"description": {"zh_CN": "不同TAB页面切换时触发该事件", "zh_TW": "不同TAB頁面切換時觸發該事件", "en_US": "Trigger this event when switching between different TAB pages"}}}, "LayoutTypeOnPageChangeHook": {"name": "onPageChange", "params": ["proxyPage", "curComponent"], "description": "兄弟页面切换时触发该事件", "lang": {"description": {"zh_CN": "兄弟页面切换时触发该事件", "zh_TW": "兄弟頁面切換時觸發該事件", "en_US": "Trigger this event when switching brother pages"}}}, "LayoutTypeOnScrollHook": {"name": "onScroll", "params": ["proxyPage", "curComponent"], "description": "组件滚动时触发该事件", "lang": {"description": {"zh_CN": "组件滚动时触发该事件", "zh_TW": "組件滾動時觸發該事件", "en_US": "Trigger this event when component scrolls"}}}, "ButtonTypeOnTapHook": {"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}, "ButtonTypeOnLongTapHook": {"name": "onLongTap", "params": ["proxyPage", "curComponent"], "description": "长按事件", "lang": {"description": {"zh_CN": "长按事件", "zh_TW": "長按事件", "en_US": "Long press event"}}}, "ButtonTypeOnDoubleTapHook": {"name": "onDoubleTap", "params": ["proxyPage", "curComponent"], "description": "双击事件", "lang": {"description": {"zh_CN": "双击事件", "zh_TW": "按兩下事件", "en_US": "Double click event"}}}, "ComponentCustomHooksMobile": {"set-up-page": [{"name": "pause", "params": ["proxyPage"], "description": "当跳转到其他页面，当前页面处于Pause状态，调用该方法", "lang": {"description": {"zh_CN": "当跳转到其他页面，当前页面处于Pause状态，调用该方法", "zh_TW": "當跳轉到其他頁面，當前頁面處於Pause狀態，調用該方法", "en_US": "When redirecting to another page and the current page is in the Pause state, call this method"}}}, {"name": "resume", "params": ["proxyPage"], "description": "当从其他页面返回该页面，当前页面处于resume状态，调用该方法", "lang": {"description": {"zh_CN": "当从其他页面返回该页面，当前页面处于resume状态，调用该方法", "zh_TW": "當從其他頁面返回該頁面，當前頁面處於resume狀態，調用該方法", "en_US": "When returning from another page and the current page is in the resume state, call this method"}}}], "set-up-drawer": [{"name": "pause", "params": ["proxyPage"], "description": "当跳转到其他页面，当前页面处于Pause状态，调用该方法", "lang": {"description": {"zh_CN": "当跳转到其他页面，当前页面处于Pause状态，调用该方法", "zh_TW": "當跳轉到其他頁面，當前頁面處於Pause狀態，調用該方法", "en_US": "When redirecting to another page and the current page is in the Pause state, call this method"}}}, {"name": "resume", "params": ["proxyPage"], "description": "当从其他页面返回该页面，当前页面处于resume状态，调用该方法", "lang": {"description": {"zh_CN": "当从其他页面返回该页面，当前页面处于resume状态，调用该方法", "zh_TW": "當從其他頁面返回該頁面，當前頁面處於resume狀態，調用該方法", "en_US": "When returning from another page and the current page is in the resume state, call this method"}}}], "DW_BUTTON_GROUP": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_BUTTON": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_BOTTOM_BUTTON": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_CUSTOM_BUTTON": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_PAGE_BUTTON": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_BUTTON_CANCEL": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_BUTTON_JUMP": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_BUTTON_RESET": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_BUTTON_STOP": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "DW_BUTTON_SUBMIT": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "单击事件", "lang": {"description": {"zh_CN": "单击事件", "zh_TW": "按一下事件", "en_US": "Click Event"}}}], "INPUT": [{"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "INPUT_NUMBER": [{"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "TEXTAREA": [{"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DW_INPUT_CALENDAR": [{"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DW_INPUT_SWITCH": [{"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DATEPICKER": [{"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "TIMEPICKER": [{"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "SELECT": [{"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DW_ATTACHMENT": [{"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "OPERATION_EDITOR": [{"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "FORM_OPERATION_EDITOR": [{"name": "onChange", "params": ["proxyPage", "curComponent"], "description": "数据录入事件，用户输入字段时，实时触发的事件", "lang": {"description": {"zh_CN": "数据录入事件，用户输入字段时，实时触发的事件", "zh_TW": "資料錄入事件，用戶輸入欄位時，實时觸發的事件", "en_US": "Data entry event, an event triggered in real-time when a user inputs a field"}}}, {"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DW_SINGLE_SELECT": [{"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DW_INPUT_SCAN": [{"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DW_BUTTON_SINGLE_SELECT": [{"name": "onComplete", "params": ["proxyPage", "curComponent"], "description": "录入完成事件，数据录入完成，组件失焦时触发", "lang": {"description": {"zh_CN": "录入完成事件，数据录入完成，组件失焦时触发", "zh_TW": "錄入完成事件，資料錄入完成，組件失焦時觸發", "en_US": "Event triggered when data entry is completed and the component is out of focus"}}}], "DW_CARD": [{"name": "onTap", "params": ["proxyPage", "curComponent"], "description": "用户单击产生的事件", "lang": {"description": {"zh_CN": "用户单击产生的事件", "zh_TW": "用戶按一下產生的事件", "en_US": "Events generated by user clicks"}}}], "DW_GROUP_CARD": [], "DW_STANDARD_TABS": [{"name": "onTabChange", "params": ["proxyPage", "curComponent"], "description": "不同TAB页面切换时触发该事件", "lang": {"description": {"zh_CN": "不同TAB页面切换时触发该事件", "zh_TW": "不同TAB頁面切換時觸發該事件", "en_US": "Trigger this event when switching between different TAB pages"}}}], "DW_SINGLE_LIST_CONTAINER": [{"name": "onScroll", "params": ["proxyPage", "curComponent"], "description": "组件滚动时触发该事件", "lang": {"description": {"zh_CN": "组件滚动时触发该事件", "zh_TW": "組件滾動時觸發該事件", "en_US": "Trigger this event when component scrolls"}}}], "DW_LIST_VIEW": [{"name": "onPageChange", "params": ["proxyPage", "curComponent"], "description": "兄弟页面切换时触发该事件", "lang": {"description": {"zh_CN": "兄弟页面切换时触发该事件", "zh_TW": "兄弟頁面切換時觸發該事件", "en_US": "Trigger this event when switching brother pages"}}}], "DW_ZTB_VIEW_LIST": [{"name": "onPageChange", "params": ["proxyPage", "curComponent"], "description": "兄弟页面切换时触发该事件", "lang": {"description": {"zh_CN": "兄弟页面切换时触发该事件", "zh_TW": "兄弟頁面切換時觸發該事件", "en_US": "Trigger this event when switching brother pages"}}}]}, "ComponentCommonHooks": [{"name": "afterInitHook", "params": ["component", "parentData", "options"], "description": "组件init后执行，只会执行一次", "lang": {"description": {"zh_CN": "组件init后执行，只会执行一次", "zh_TW": "元件init後執行，只會執行一次", "en_US": "Component initialization, executed only once"}}}, {"name": "afterViewInitHook", "params": ["component", "parentData", "options"], "description": "组件视图完成后调用", "lang": {"description": {"zh_CN": "组件视图完成后调用", "zh_TW": "元件視圖完成後呼叫", "en_US": "Called after the component view is completed"}}}, {"name": "valueChangesHook", "params": ["component", "value", "parentData", "options", "changes"], "description": "组件绑定的control值变化时执行", "lang": {"description": {"zh_CN": "组件绑定的control值变化时执行", "zh_TW": "元件綁定的control值變化時執行", "en_US": "Executed when the control value bound to the component changes"}}}, {"name": "statusChangesHook", "params": ["component", "status", "options"], "description": "组件绑定的control状态变化时执行", "lang": {"description": {"zh_CN": "组件绑定的control状态变化时执行", "zh_TW": "元件綁定的control狀態變化時執行", "en_US": "Executed when the control state bound to the component changes"}}}, {"name": "beforeDestroyHook", "params": ["component", "parentData", "options"], "description": "组件销毁前触发", "lang": {"description": {"zh_CN": "组件销毁前触发", "zh_TW": "元件銷毀前觸發", "en_US": "Triggered before the component is destroyed"}}}], "ComponentCustomHookParams": ["component", "parentData", "options"], "ComponentCustomHooks": {"BUTTON": [{"name": "clickHook", "params": ["component", "parentData", "options", "e"], "description": "按钮点击的钩子", "lang": {"description": {"zh_CN": "按钮点击的钩子", "zh_TW": "按鈕點擊的鈎子", "en_US": "Hook for button click"}}}, {"name": "dbClickHook", "params": ["component", "parentData", "options", "e"], "description": "按钮双击的钩子", "lang": {"description": {"zh_CN": "按钮双击的钩子", "zh_TW": "按鈕雙擊的鈎子", "en_US": "Hook for button double-click"}}}, {"name": "mouseleaveHook", "params": ["component", "parentData", "options", "e"], "description": "按钮鼠标移出的钩子", "lang": {"description": {"zh_CN": "按钮鼠标移出的钩子", "zh_TW": "按鈕鼠標移出的鈎子", "en_US": "Hook for button mouse out"}}}, {"name": "mouseenterHook", "params": ["component", "parentData", "options", "e"], "description": "按钮鼠标移入的钩子", "lang": {"description": {"zh_CN": "按钮鼠标移入的钩子", "zh_TW": "按鈕鼠標移入的鈎子", "en_US": "Hook for button mouse over"}}}, {"name": "buttonInitHook", "params": ["button", "dataControl", "options", "component", "callback"], "description": "按钮初始化时hooks", "lang": {"description": {"zh_CN": "按钮初始化时hooks", "zh_TW": "按鈕初始化時hooks", "en_US": "Hooks during button initialization"}}}, {"name": "beforeClickHook", "params": ["button", "dataControl", "options", "component", "callback"], "description": "按钮点击前hooks，可中断原程序", "lang": {"description": {"zh_CN": "按钮点击前hooks，可中断原程序", "zh_TW": "按鈕點擊前hooks，可中斷原程式", "en_US": "Hooks before clicking the button can interrupt the original program"}}}, {"name": "afterClickHook", "params": ["button", "dataControl", "options", "component", "callback"], "description": "按钮点击后hooks", "lang": {"description": {"zh_CN": "按钮点击后hooks", "zh_TW": "按鈕點擊後hooks", "en_US": "Hooks after button click"}}}], "FORM_LIST": [], "INPUT": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "INPUT_NUMBER": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "MEASURE": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "PERCENT_INPUT": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "TEXTAREA": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "GRIDSTER": [{"name": "gridInit", "params": ["component", "parentData", "options", "e"], "description": "网格组件初始化触发", "lang": {"description": {"zh_CN": "网格组件初始化触发", "zh_TW": "網格組件初始化觸發", "en_US": "Grid component initialization trigger"}}}, {"name": "itemChange", "params": ["component", "parentData", "options", "e"], "description": "每一个item发生变化时触发（包括x、y坐标和cols、rows）", "lang": {"description": {"zh_CN": "每一个item发生变化时触发（包括x、y坐标和cols、rows）", "zh_TW": "每一個item發生變化時觸發（包括x、y座標和cols、rows）", "en_US": "Triggered when each item changes (including x, y coordinates and cols, rows)"}}}], "AMOUNT_INPUT": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "SELECT": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "SELECT_MULTIPLE": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "DATEPICKER": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "DATE_RANGE": [], "PERSON_SELECT": [], "PERSON_SELECT_NEW": [], "COLLAPSE": [], "TIMEPICKER": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "focusHook", "params": ["component", "parentData", "options", "e"], "description": "聚焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "聚焦的钩子，供定制前端配置脚本", "zh_TW": "聚焦的鈎子，供定製前端配置腳本", "en_US": "Focus hook, for customizing front-end configuration scripts"}}}], "TIME_RANGE": [], "RADIO_GROUP": [], "CHECKBOX": [], "EOC_SELECT": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}], "EOC_SELECT_NEW": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}], "EOC_MULTI_SELECT": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}], "EOC_MULTI_SELECT_NEW": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}], "EOC_USER_SELECT": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}], "EOC_USER_SELECT_NEW": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}], "OPERATION_EDITOR": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "afterCloseHook", "params": ["component", "parentData", "options", "e"], "description": "开窗关闭的钩子,供定制前端配置脚本", "lang": {"description": {"zh_CN": "开窗关闭的钩子,供定制前端配置脚本", "zh_TW": "開窗關閉的鈎子,供定製前端配置腳本", "en_US": "Window closure hooks for custom front-end configuration scripts"}}}, {"name": "beforeSearchHook'", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗组件内，点击搜索按钮前立即执行", "lang": {"description": {"zh_CN": "开窗组件内，点击搜索按钮前立即执行", "zh_TW": "開窗元件內，點擊搜尋按鈕前立即執行", "en_US": "Within the window component, click the search button to execute immediately"}}}, {"name": "searchConditionChangeHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗组件内，搜索条件变化时执行", "lang": {"description": {"zh_CN": "开窗组件内，搜索条件变化时执行", "zh_TW": "開窗元件內，搜尋條件變化時執行", "en_US": "Execute when search conditions change within the window component"}}}, {"name": "tableBeforeRenderHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格渲染前执行", "lang": {"description": {"zh_CN": "开窗内表格渲染前执行", "zh_TW": "開窗內表格渲染前執行", "en_US": "Execute before rendering the table inside the window"}}}, {"name": "tableGridReadyHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格初始化后执行", "lang": {"description": {"zh_CN": "开窗内表格初始化后执行", "zh_TW": "開窗內表格初始化後執行", "en_US": "Execute after initializing the table inside the window"}}}, {"name": "tableRowSelectedHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格行选中时执行", "lang": {"description": {"zh_CN": "开窗内表格行选中时执行", "zh_TW": "開窗內表格行選中時執行", "en_US": "Execute when selecting a table row in the window"}}}, {"name": "tableSelectionChangedHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格行选中变化时执行", "lang": {"description": {"zh_CN": "开窗内表格行选中变化时执行", "zh_TW": "開窗內表格行選中變化時執行", "en_US": "Execute when selecting changes in the table rows within the window"}}}, {"name": "tablePaginationChangedHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格分页变化(pageSize或pageNumber变化)时执行", "lang": {"description": {"zh_CN": "开窗内表格分页变化(pageSize或pageNumber变化)时执行", "zh_TW": "開窗內表格分頁變化(pageSize或pageNumber變化)時執", "en_US": "Execute when there is a page size or page number change in the table within the window"}}}, {"name": "beforeSubmitHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内点击底部按钮提交前执行", "lang": {"description": {"zh_CN": "开窗内点击底部按钮提交前执行", "zh_TW": "開窗內點擊底部按鈕提交前執行", "en_US": "Click the bottom button inside the window to execute before submitting"}}}, {"name": "afterSubmitHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内点击底部按钮提交，请求后台接口完成后执行，非接口请求不执行", "lang": {"description": {"zh_CN": "开窗内点击底部按钮提交，请求后台接口完成后执行，非接口请求不执行", "zh_TW": "開窗內點擊底部按鈕提交，請求後台界面完成後執行，非界面請求不執行", "en_US": "Click the bottom button in the window to submit and request the backend interface to complete before execution. Non interface requests will not be executed"}}}, {"name": "afterWindowFillBackHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内点击底部按钮提交，回填字段后执行", "lang": {"description": {"zh_CN": "开窗内点击底部按钮提交，回填字段后执行", "zh_TW": "開窗內點擊底部按鈕提交，回填欄位後執行", "en_US": "Click the bottom button inside the window to submit, fill in the fields, and then execute"}}}], "FORM_OPERATION_EDITOR": [{"name": "blurHook", "params": ["component", "parentData", "options", "e"], "description": "失焦的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "失焦的钩子，供定制前端配置脚本", "zh_TW": "失焦的鈎子，供定製前端配置腳本", "en_US": "Blur hook, for customizing front-end configuration scripts"}}}, {"name": "afterCloseHook", "params": ["component", "parentData", "options", "e"], "description": "开窗关闭的钩子,供定制前端配置脚本", "lang": {"description": {"zh_CN": "开窗关闭的钩子,供定制前端配置脚本", "zh_TW": "開窗關閉的鈎子,供定製前端配置腳本", "en_US": "Window closure hooks for custom front-end configuration scripts"}}}, {"name": "beforeSearchHook'", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗组件内，点击搜索按钮前立即执行", "lang": {"description": {"zh_CN": "开窗组件内，点击搜索按钮前立即执行", "zh_TW": "開窗元件內，點擊搜尋按鈕前立即執行", "en_US": "Within the window component, click the search button to execute immediately"}}}, {"name": "searchConditionChangeHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗组件内，搜索条件变化时执行", "lang": {"description": {"zh_CN": "开窗组件内，搜索条件变化时执行", "zh_TW": "開窗元件內，搜尋條件變化時執行", "en_US": "Execute when search conditions change within the window component"}}}, {"name": "tableBeforeRenderHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格渲染前执行", "lang": {"description": {"zh_CN": "开窗内表格渲染前执行", "zh_TW": "開窗內表格渲染前執行", "en_US": "Execute before rendering the table inside the window"}}}, {"name": "tableGridReadyHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格初始化后执行", "lang": {"description": {"zh_CN": "开窗内表格初始化后执行", "zh_TW": "開窗內表格初始化後執行", "en_US": "Execute after initializing the table inside the window"}}}, {"name": "tableRowSelectedHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格行选中时执行", "lang": {"description": {"zh_CN": "开窗内表格行选中时执行", "zh_TW": "開窗內表格行選中時執行", "en_US": "Execute when selecting a table row in the window"}}}, {"name": "tableSelectionChangedHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格行选中变化时执行", "lang": {"description": {"zh_CN": "开窗内表格行选中变化时执行", "zh_TW": "開窗內表格行選中變化時執行", "en_US": "Execute when selecting changes in the table rows within the window"}}}, {"name": "tablePaginationChangedHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内表格分页变化(pageSize或pageNumber变化)时执行", "lang": {"description": {"zh_CN": "开窗内表格分页变化(pageSize或pageNumber变化)时执行", "zh_TW": "開窗內表格分頁變化(pageSize或pageNumber變化)時執", "en_US": "Execute when there is a page size or page number change in the table within the window"}}}, {"name": "beforeSubmitHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内点击底部按钮提交前执行", "lang": {"description": {"zh_CN": "开窗内点击底部按钮提交前执行", "zh_TW": "開窗內點擊底部按鈕提交前執行", "en_US": "Click the bottom button inside the window to execute before submitting"}}}, {"name": "afterSubmitHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内点击底部按钮提交，请求后台接口完成后执行，非接口请求不执行", "lang": {"description": {"zh_CN": "开窗内点击底部按钮提交，请求后台接口完成后执行，非接口请求不执行", "zh_TW": "開窗內點擊底部按鈕提交，請求後台界面完成後執行，非界面請求不執行", "en_US": "Click the bottom button in the window to submit and request the backend interface to complete before execution. Non interface requests will not be executed"}}}, {"name": "afterWindowFillBackHook", "params": ["component", "parentData", "options", "dataControl", "openWindowParams", "tableComponent", "value"], "description": "开窗内点击底部按钮提交，回填字段后执行", "lang": {"description": {"zh_CN": "开窗内点击底部按钮提交，回填字段后执行", "zh_TW": "開窗內點擊底部按鈕提交，回填欄位後執行", "en_US": "Click the bottom button inside the window to submit, fill in the fields, and then execute"}}}], "FORM_UPLOAD": [], "FILE_UPLOAD": [], "TABS": [{"name": "selectChangeHook", "params": ["component", "parentData", "options", "e"], "description": "tab选择变更的钩子，供定制前端配置脚本", "lang": {"description": {"zh_CN": "tab选择变更的钩子，供定制前端配置脚本", "zh_TW": "tab選擇變更的鈎子，供定製前端配置腳本", "en_US": "Hook for tab selection change, for customizing front-end configuration scripts"}}}], "DRAWER_BUTTON": [], "ATHENA_TABLE": [{"name": "toolbarChangedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "表格右上角设置、多列多字段排序弹窗事件", "lang": {"description": {"zh_CN": "表格右上角设置、多列多字段排序弹窗事件", "zh_TW": "表格右上角設定、多列多欄位排序彈窗事件", "en_US": "Event for table settings in the upper right corner and sorting window with multiple columns and fields"}}}, {"name": "paginationChangedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "表格页码或页数变化时触发", "lang": {"description": {"zh_CN": "表格页码或页数变化时触发", "zh_TW": "表格頁碼或頁數變化時觸發", "en_US": "Triggered when the table page number or page size changes"}}}, {"name": "pageNumberChangedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "分页页码pageNumber变化时触发", "lang": {"description": {"zh_CN": "分页页码pageNumber变化时触发", "zh_TW": "分頁頁碼pageNumber變化時觸發", "en_US": "Triggered when the pagination page number (pageNumber) changes"}}}, {"name": "pageSizeChangedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "分页数据pageSize变化时触发", "lang": {"description": {"zh_CN": "分页数据pageSize变化时触发", "zh_TW": "分頁數據pageSize變化時觸發", "en_US": "Triggered when the pagination data page size (pageSize) changes"}}}, {"name": "columnResizedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "列调整后触发的事件", "lang": {"description": {"zh_CN": "列调整后触发的事件", "zh_TW": "列調整後觸發的事件", "en_US": "Event triggered after column adjustment"}}}, {"name": "rowClickedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "行点击触发的事件", "lang": {"description": {"zh_CN": "行点击触发的事件", "zh_TW": "行點擊觸發的事件", "en_US": "Event triggered by row click"}}}, {"name": "rowSelectedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "行被选择，可获取当前行节点", "lang": {"description": {"zh_CN": "行被选择，可获取当前行节点", "zh_TW": "行被選擇，可獲取當前行節點", "en_US": "Row selected, can get the current row node"}}}, {"name": "rowValueChangesHook", "params": ["component", "parentData", "options", "value", "changes", "CustomEvent"], "description": "表格行数据发生变动后触发的事件，任一单元格值变化均会触发", "lang": {"description": {"zh_CN": "表格行数据发生变动后触发的事件，任一单元格值变化均会触发", "zh_TW": "表格行數據發生變動後觸發的事件，任一儲存格值變化均會觸發", "en_US": "An event triggered after a change in table row data, where any change in cell value will trigger"}}}, {"name": "selectionChangedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "行被选择，此时可以通过api获取已选择的行数据，晚于rowSelected", "lang": {"description": {"zh_CN": "行被选择，此时可以通过api获取已选择的行数据，晚于rowSelected", "zh_TW": "行被選擇，此時可以通過api獲取已選擇的行數據，晚於rowSelected", "en_US": "Row selected, can get the selected row data through the API, later than rowSelected"}}}, {"name": "gridReadyHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "表格已经初始化完毕", "lang": {"description": {"zh_CN": "表格已经初始化完毕", "zh_TW": "表格已經初始化完畢", "en_US": "Table has been initialized"}}}, {"name": "filterChangedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "表格发生了过滤操作", "lang": {"description": {"zh_CN": "表格发生了过滤操作", "zh_TW": "表格發生了過濾操作", "en_US": "Table has undergone filtering operation"}}}, {"name": "sortChangedHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "表格发生了排序操作", "lang": {"description": {"zh_CN": "表格发生了排序操作", "zh_TW": "表格發生了排序操作", "en_US": "Table has undergone sorting operation"}}}, {"name": "scrollToBottomChangeHook", "params": ["component", "parentData", "options", "CustomEvent"], "description": "表格滚动条滚动到了底部", "lang": {"description": {"zh_CN": "表格滚动条滚动到了底部", "zh_TW": "表格滾動條滾動到了底部", "en_US": "Table scrollbar has scrolled to the bottom"}}}], "FLEX": [{"name": "clickHook", "params": ["component", "parentData", "options", "e"], "description": "点击事件的钩子", "lang": {"description": {"zh_CN": "点击事件的钩子", "zh_TW": "點擊事件的鈎子", "en_US": "Hook for click"}}}, {"name": "dbClickHook", "params": ["component", "parentData", "options", "e"], "description": "双击事件的钩子", "lang": {"description": {"zh_CN": "双击事件的钩子", "zh_TW": "雙擊事件的鈎子", "en_US": "Hook for double-click"}}}, {"name": "mouseleaveHook", "params": ["component", "parentData", "options", "e"], "description": "按钮鼠标移出的钩子", "lang": {"description": {"zh_CN": "按钮鼠标移出的钩子", "zh_TW": "按鈕鼠標移出的鈎子", "en_US": "Hook for button mouse out"}}}, {"name": "mouseenterHook", "params": ["component", "parentData", "options", "e"], "description": "按钮鼠标移入的钩子", "lang": {"description": {"zh_CN": "按钮鼠标移入的钩子", "zh_TW": "按鈕鼠標移入的鈎子", "en_US": "Hook for button mouse over"}}}], "TEXT": [], "ICON": [], "IMAGE": [], "DIVIDER": [], "LIST": [], "DATA_QUERY": [], "MODAL": []}, "DataCommonHooks": [{"name": "valueChangesHook", "params": ["dataControl", "value", "parentData", "options", "changes"], "description": "数据对象值变更", "lang": {"description": {"zh_CN": "数据对象值变更", "zh_TW": "數據對象值變更", "en_US": "Data object value has changed"}}, "dataType": ["object", "array", "basicData"]}, {"name": "afterResetHook", "params": ["dataControl", "parentData", "options"], "description": "重置数据对象之后", "lang": {"description": {"zh_CN": "重置数据对象之后", "zh_TW": "重置數據對象之後", "en_US": "After resetting the data object"}}, "dataType": ["object", "array", "basicData"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "params": ["dataControl", "value", "parentData", "options", "changes"], "description": "添加数据对象之后", "lang": {"description": {"zh_CN": "添加数据对象之后", "zh_TW": "添加數據對象之後", "en_US": "After adding the data object"}}, "dataType": ["object", "array"]}, {"name": "afterRemoveHook", "params": ["dataControl", "value", "parentData", "options", "changes"], "description": "移除数据对象之后", "lang": {"description": {"zh_CN": "移除数据对象之后", "zh_TW": "移除數據對象之後", "en_US": "After removing the data object"}}, "dataType": ["object", "array"]}, {"name": "afterSetHook", "params": ["dataControl", "value", "parentData", "options", "changes"], "description": "设置数据对象之后", "lang": {"description": {"zh_CN": "设置数据对象之后", "zh_TW": "設定數據對象之後", "en_US": "After setting the data object"}}, "dataType": ["object", "array"]}, {"name": "afterClearHook", "params": ["dataControl", "parentData", "options"], "description": "移除所有数据对象之后", "lang": {"description": {"zh_CN": "移除所有数据对象之后", "zh_TW": "移除所有數據對象之後", "en_US": "After removing all data objects"}}, "dataType": ["object", "array"]}], "SubpageButtonHooks": [{"name": "afterSubpageCloseHook", "params": ["component", "parentData", "options"], "description": "弹框或抽屉关闭hook", "lang": {"description": {"zh_CN": "弹框或抽屉关闭hook", "zh_TW": "彈框或抽屜關閉hook", "en_US": "Pop-up box or drawer closing hook"}}}], "SubmitActionsHooks": [{"name": "beforeVerifyHook", "params": ["button", "dataControl", "options", "component", "callback"], "description": "底部操作按钮独有，beforeClickHook 之后 ，数据校验之前，可中断原程序", "lang": {"description": {"zh_CN": "底部操作按钮独有，beforeClickHook 之后 ，数据校验之前，可中断原程序", "zh_TW": "底部操作按鈕獨有，beforeClickHook之後，數據校驗之前，可中斷原程式", "en_US": "'The bottom operation button is unique, and after foreClickHook and before data verification, the original program can be interrupted'"}}}, {"name": "beforeSubmitHook", "params": ["button", "dataControl", "options", "component", "callback"], "description": "底部操作按钮独有，请求接口之前执行的hooks，可中断原程序", "lang": {"description": {"zh_CN": "底部操作按钮独有，请求接口之前执行的hooks，可中断原程序", "zh_TW": "底部操作按鈕獨有，請求介面之前執行的hooks，可中斷原程式", "en_US": "'The bottom operation button is unique, and the hooks executed before the request interface can interrupt the original program'"}}}, {"name": "afterSubmitHook", "params": ["button", "dataControl", "response", "options", "component", "callback"], "description": "底部操作按钮独有，请求接口成功之后执行的hooks", "lang": {"description": {"zh_CN": "底部操作按钮独有，请求接口成功之后执行的hooks'", "zh_TW": "底部操作按鈕獨有，請求介面成功之後執行的hooks", "en_US": "The bottom operation button is unique to hooks executed after a successful request interface"}}}], "ParamDescription": {"component": "当前组件实例的代理对象", "options": "hooks 中工具服务集合，无状态，提供纯函数工具", "parentData": "父级数据，只读", "value": "数据值", "status": "组件数据狀態 type FormControlStatus = \"VALID\" | \"INVALID\" | \"PENDING\" | \"DISABLED\"", "e": "e", "event": "e", "CustomEvent": "e", "dataControl": "数据控制对象", "index": "當前數據的索引", "button": "按鈕组件实例", "response": "請求的嚮應", "changes": "包含preValue、path+schema 等更详细信息", "callback": "开窗回填数据的回调函数", "openWindowParams": "开窗参数", "tableComponent": "基础表格实例"}, "HookEvent": [{"name": "clickHook", "event": "PointerEvent"}, {"name": "dbClickHook", "event": "PointerEvent"}, {"name": "mouseleaveHook", "event": "MouseEvent"}, {"name": "mouseenterHook", "event": "MouseEvent"}, {"name": "blurHook", "event": "FocusEvent"}, {"name": "focusHook", "event": "FocusEvent"}, {"name": "selectChangeHook", "event": "NzTabChangeEvent"}, {"name": "afterCloseHook", "event": "Visible"}], "EventEnum": {"PointerEvent": "PointerEvent", "MouseEvent": "MouseEvent", "FocusEvent": "FocusEvent", "NzTabChangeEvent": "NzTabChangeEvent", "Visible": "Visible"}, "HooksTemplateInfo": [{"key": "dataOperation", "label": "数据操作", "lang": {"label": {"zh_CN": "数据操作", "zh_TW": "數據操作", "en_US": "Data Operations"}}, "children": [{"key": "dataVerification", "label": "数据校验", "lang": {"label": {"zh_CN": "数据校验", "zh_TW": "數據校驗", "en_US": "Data Validate"}}, "template": "if (value === 1) {\n  dataControl.control.setErrors({\n    customInvalid: {\n      key: 'customInvalid',\n      type: 'error',\n      errorMessage: '输入错误',\n    },\n  });\n} else {\n  if (dataControl.control.hasError('customInvalid')) {\n    dataControl.control.setErrors({\n      customInvalid: null,\n    });\n  }\n}"}, {"key": "dataCache", "label": "数据缓存", "lang": {"label": {"zh_CN": "数据缓存", "zh_TW": "數據校驗", "en_US": "<PERSON>"}}, "template": "const tableLength =component.getAllRowData().length \n // 缓存表格长度\n options.dataStore.observableObj({ key: 'tableLength', target: tableLength })"}]}, {"key": "componentOperation", "label": "组件操作", "lang": {"label": {"zh_CN": "组件操作", "zh_TW": "元件操作", "en_US": "component operation"}}, "children": [{"key": "showOrHide", "label": "显示/隐藏", "lang": {"label": {"zh_CN": "显示/隐藏", "zh_TW": "顯示/隱藏", "en_US": "show/hidden"}}, "template": "const tableData = component.getAllRowData();\nlet main_unit = tableData[0].value.main_unit;\nif (main_unit === '0') {\n  component.setColumnVisible('main_unit', false);\n}"}]}, {"key": "messageOperation", "label": "消息操作", "lang": {"label": {"zh_CN": "消息操作", "zh_TW": "消息操作", "en_US": "message operation"}}, "children": [{"key": "sendMessage", "label": "发送消息", "lang": {"label": {"zh_CN": "发送消息", "zh_TW": "發送消息", "en_US": "Send Message"}}, "template": "options.utils.configService.get('aimUrl').subscribe((url) => {\n\tconst url = `${url}/api/aim/v2/message/center/send`;\n\t// 模拟接口参数\n\tconst params = {\n\t\ttenantId: '',\n\t\tappCode: '',\n\t\tsceneId: '',\n\t\tdata: {},\n\t};\n\toptions.utils.http.post(url, params).subscribe((res) => {\n\t\tconsole.log('===接口响应结果为===', res);\n\t});\n});"}]}], "HooksDemoQuestions": [{"question": "日期控件初始化时，将当前时间格式化为“年月日时分秒”，作为默认值赋值", "lang": {"question": {"zh_CN": "日期控件初始化时，将当前时间格式化为“年月日时分秒”,作为默认值赋值", "zh_TW": "日期控件初始化時，將當前時間格式化為“年月日時分秒”,作為預設值賦值", "en_US": "Date control initialization, the current time is formatted as \"year month day hour minute second\", and the default value is assigned"}}}, {"question": "部门栏位组件初始化后， 如果值为空，则调取esp接口，入参为action（afterInitHook）", "lang": {"question": {"zh_CN": "部门栏位组件初始化后， 如果值为空，则调取esp接口，入参为action（afterInitHook）", "zh_TW": "部門欄位元件初始化後， 如果值為空則調取esp接口，入參為action（afterInitHook）", "en_US": "Department column component initialization after, if the value is empty, call the esp interface, the entry parameter is action (afterInitHook)"}}}, {"question": "表格初始化完后，获取表格第一条数据中main_unit的值，如果值为‘0’，把表格中的complete_rework_work_hours栏位隐藏", "lang": {"question": {"zh_CN": "表格初始化完后，获取表格第一条数据中main_unit的值，如果值为‘0’，把表格中的complete_rework_work_hours栏位隐藏", "zh_TW": "表格初始化完後，獲取表格第一條數據中main_unit的值，如果值為‘0’，將表格中的complete_rework_work_hours欄位隱藏", "en_US": "After the table is initialized, get the value of the main_unit in the first row of the table, if the value is '0', hide the complete_rework_work_hours column in the table"}}}, {"question": "headerName（标题）为【客户】，组件类型为OPERATION_EDITOR值发生变更时，将表格数据清空掉", "lang": {"question": {"zh_CN": "headerName（标题）为【客户】，组件类型为OPERATION_EDITOR值发生变更时，将表格数据清空掉", "zh_TW": "headerName（標題）為【客戶】，組件類型為OPERATION_EDITOR值發生變更時，將表格數據清空掉", "en_US": "The headerName (title) is [Customer], the component type is OPERATION_EDITOR value changes, the table data is cleared"}}}, {"question": "表格新增行后，将新增行中所有字段标记为脏", "lang": {"question": {"zh_CN": "表格新增行后，将新增行中所有字段标记为脏", "zh_TW": "表格新增行後，將新增行中所有欄位標記為脏", "en_US": "After adding a new row to the table, all fields in the new row are marked as dirty"}}}]}