import { Injectable } from '@angular/core';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, Subject, forkJoin } from 'rxjs';
import { filter } from 'rxjs/operators';
import { AppService } from 'pages/apps/app.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EPerspective, UpdateMenuByOperation, menuOpenEum } from '../../utils/operation';
import { merge } from 'lodash';
import { cloneDeep, isEmpty } from 'common/utils/core.utils';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { MdWebapiService } from '../../service/md-webapi.service';
import { ModelDrivenIntroService } from 'common/service/intro/model-driven-intro/model-driven-intro.service';

// 检测渲染内容对象
// 可以通过传递CheckObject检测对象，在menu切换时触发检测
// 也可以直接设置isContentChangeWithoutSave，则跳过检测方法，直接根据isContentChangeWithoutSave判断是否存在未保存的变化
interface ContentChangeCheckObject {
  checkObject?: CheckObject;
  isContentChangeWithoutSave?: boolean;
}

interface CheckObject {
  contentComponent?: any; // 渲染组件的实例
  checkFunction?: string; // 检测方法checkFunction
}

@Injectable()
export class AppModelDrivenService {
  adesignerUrl: string;

  // 业务搭建的tab
  tabIndex: number = 0;

  private _businessObjectMenu$ = new BehaviorSubject<any>([]); // 业务对象菜单
  get businessObjectMenu$(): Observable<any> {
    return this._businessObjectMenu$.asObservable().pipe(filter((data) => data !== null));
  } // 订阅使用
  get businessObjectMenu() {
    // 直接取值时使用
    return this._businessObjectMenu$.getValue();
  }
  setBusinessObjectMenu(data) {
    // 改变值
    this._businessObjectMenu$.next(data);
  }

  // 资源视角下
  private _resourceObjectMenu$ = new BehaviorSubject<any>([]); // 资源视角下
  get resourceObjectMenu$(): Observable<any> {
    return this._resourceObjectMenu$.asObservable().pipe(filter((data) => data !== null));
  } // 订阅使用

  get resourceObjectMenu() {
    // 直接取值时使用
    return this._resourceObjectMenu$.getValue();
  }
  setResourceObjectMenu(data) {
    // 改变值
    this._resourceObjectMenu$.next(data);
  }

  private _instrumentPaneMenu$ = new BehaviorSubject<any>([]); // 仪表盘菜单
  get instrumentPaneMenu$(): Observable<any> {
    return this._instrumentPaneMenu$.asObservable().pipe(filter((data) => data !== null));
  } //订阅使用
  get instrumentPaneMenu() {
    // 直接取值时使用
    return this._instrumentPaneMenu$.getValue();
  }
  setInstrumentPaneMenu(data) {
    // 改变值
    this._instrumentPaneMenu$.next(data);
  }

  _instrumentPaneShow$ = new BehaviorSubject<any>(null); // 仪表盘展示按钮
  get instrumentPaneShow$(): Observable<any> {
    return this._instrumentPaneShow$.asObservable().pipe(filter((data) => data !== null));
  }

  dataViewRefresh$: Subject<any> = new Subject(); // 数据视图页面更新,名称不可以改，与this[refresh]映射
  pageDesignRefresh$: Subject<any> = new Subject(); // 作业页面更新，名称不可以改， 与this[refresh]映射
  businessProcessRefresh$: Subject<any> = new Subject(); // 流程设计页面更新，名称不可以改， 与this[refresh]映射

  private _contentSaveLoading: boolean = false; // 内容页面是否正在保存loading（包含保存，发布，生成视图时的loading状态）
  get contentSaveLoading() {
    return this._contentSaveLoading;
  }

  // 渲染内容变化但未保存的检测对象，具体逻辑可以依据当前组件的改造难度灵活实现
  private _contentChangeCheckObject: ContentChangeCheckObject = null;
  get contentChangeCheckObject() {
    return this._contentChangeCheckObject;
  }

  subRouteData: any; // 激活的路由
  redirectionUrl: any; // 存取 以便于重定向优化
  clickMenuBackData$: Subject<any> = new Subject(); // 点击菜单menu返回事件
  fastAddBackData$: Subject<any> = new Subject(); // 快速点击menu事件
  menuOpenEum = menuOpenEum; // 'data-view', 'page-design', 'event', 'service', 'business-process'
  crumbs: Array<string> = []; // 面包屑
  currentActiveMenuType: string; // 当前路由菜单类型

  _exchangePageDesignType$ = new BehaviorSubject<'pc' | 'mobile'>('pc'); // 界面设计移动端/app端口子
  get exchangePageDesignType$(): Observable<any> {
    return this._exchangePageDesignType$.asObservable().pipe(filter((data) => data !== null));
  }

  sidebarWidth = 200;
  isReSize = false; // 是否正在拖动

  isUpdateBusinessData$: Subject<any> = new Subject();

  loadingPage: boolean = false; // 页面加载loading

  constructor(
    public appService: AppService,
    protected http: HttpClient,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private message: NzMessageService,
    private translateService: TranslateService,
    private languageService: LocaleService,
    private mdWebapiService: MdWebapiService,
    protected configService: SystemConfigService,
    private modelDrivenIntroService: ModelDrivenIntroService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  /**
   * 设置仪表盘展示关闭
   */
  changeInstrumentPane() {
    this._instrumentPaneShow$.next(!this._instrumentPaneShow$.getValue());
  }

  /**
   * 初始化时候，一次性获取业务对象菜单和仪表盘菜单
   * @param appCode
   * @returns
   */
  getBusinessConstructionMenu(route) {
    const appCode = route.queryParams.appCode;
    this.redirectionUrl = '';
    return new Observable((subscriber) => {
      // 获取对象视角下的
      forkJoin([
        this.mdWebapiService.getBusinessObjectMenuList(appCode),
        this.mdWebapiService.getInstrumentPaneMenuList(appCode),
      ]).subscribe(
        (res) => {
          let url, data;
          if (res) {
            const { appCode, introTaskNum } = route?.queryParams ?? {};
            // 是否需要新手引导
            const isIntro = this.modelDrivenIntroService.isModelDrivenIntro({ application: appCode, introTaskNum });
            // 新手引导需展开所有树节点 jytodo 资源树展开要有不同的处理
            const menuData = isIntro
              ? (res[0]?.data ?? []).map((business) => {
                  return {
                    ...business,
                    businessDirTree: (business?.businessDirTree ?? []).map((dirTree) => {
                      const dirTreeLength = (dirTree.businessDirTree ?? []).length;
                      return dirTreeLength > 0 ? { ...dirTree, open: true } : dirTree;
                    }),
                  };
                })
              : res[0]?.data ?? [];
            this._businessObjectMenu$.next(menuData || []);
            this._instrumentPaneMenu$.next(res[1]?.data || []);
            url = this.handleActiveSubMenu(route);
            data = {
              businessObjectMenu: menuData,
              instrumentPaneMenu: res[1],
            };
          }
          subscriber.next({
            data,
            url,
          });
        },
        (error) => {
          subscriber.error(error);
        },
      );
      // 获取资源视角下的
      this.mdWebapiService.getBusinessObjectMenuListResource(appCode).subscribe(
        (res) => {
          const resourceMenu = res?.data?.businessDirTree || [];
          if (route?._routerState?.url) {
            const urlPath = route?._routerState?.url.split('?')[0].replace('/app/business-constructor/', '');
            let result = [];
            this.getPathByKey(urlPath, resourceMenu, result);
            result.forEach((item) => {
              if (!item.open) item.open = true;
            });
          }
          this._resourceObjectMenu$.next(resourceMenu);
        },
        (error) => {},
      );
    });
  }

  handleRouterLinkTo(path) {
    if (!path) return;

    this.resetContentChangeCheckObject();

    if (path.includes('page-design')) {
      this.onExchangePageDesignType('pc');
    }
    this.router.navigate([`/app/business-constructor/${path}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  /**
   * 更新更新业务对象菜单并做系列处理
   * @param param
   *  businessCode?: string; // operatorType == 'add' && menuType === 'businessConstructor'（新增业务对象） 时可不传，其他必填,防止后面调用接口做局部刷新
   *  menuType: 'businessConstructor'| 'dataView' | 'pageDesign' | 'event'  |  'service' | 'businessProcess'
   *  operatorType: 'add' | 'edit' | 'delete';
   *  deleteId?: string; // operatorType = delete （删除）时 必填
   *  editId?: string; // 编辑id,编辑的时候需要传，为了查询更新面包屑 以及对应页面刷新
   *  navigateUrl //  新增时 会返回一个url提供跳转
   */
  updateBusinessObjectMenuRefresh(param: UpdateMenuByOperation) {
    // 刷新资源视角下的路由
    this.updateResourceObjectMenuRefresh(param);
    // 刷新对象视角下的路由
    this.updateObjectMenuRefresh(param);
  }

  /**
   * 刷新对象视角下的路由
   * @param param
   */
  private updateObjectMenuRefresh(param: UpdateMenuByOperation) {
    const { menuType, businessCode } = param;
    // 默认查所有业务对象，只局部刷新'dataView', 'pageDesign', 'businessProcess','dataViewAndPageDesign'
    if (!this.businessObjectMenu?.some((e) => e.businessCode === businessCode)) {
      this.updateBusinessObjectMenu(param);
    } else if (['dataView', 'pageDesign', 'businessProcess'].includes(menuType)) {
      if (businessCode) {
        // 对象视角下新增 作业、查询方案 能确定businessCode，所以局部刷新
        this.updateBusinessObjectSubmenu({
          ...param,
          menuType: menuType === 'pageDesign' ? 'homeWork' : menuType,
        });
      } else {
        // 资源视角下新增 作业、查询方案 不能确定businessCode，所以要全局刷新
        this.updateBusinessObjectMenu(param);
      }
    } else if (['service', 'event', 'detect', 'modelDesign'].includes(menuType)) {
      if (param.operatorType === 'delete' && 'modelDesign' === menuType) {
        this.updateBusinessObjectMenu(param);
      } else {
        this.updateBusinessObjectSubmenu(param);
      }
    } else if (menuType === 'businessConstructor') {
      this.updateBusinessObjectMenu(param);
    }
  }

  /**
   * 更新资源视角的菜单
   * @param param
   */
  async updateResourceObjectMenuRefresh(param: UpdateMenuByOperation) {
    if (
      param.menuType === 'businessConstructor' ||
      ('modelDesign' === param.menuType && param.operatorType === 'delete')
    ) {
      // 刷新整个资源视角下的树
      const result = await this.mdWebapiService
        .getBusinessObjectMenuListResource(this.appService.selectedApp.code)
        .toPromise();
      if (result.code === 0) {
        const tree = result.data.businessDirTree || [];
        this.handleOpenKeys(this.resourceObjectMenu, tree);
        this._resourceObjectMenu$.next(tree);
      }
    } else {
      this.updateResourceObjectMenuPartial(param);
    }
  }

  /**
   * 更新资源视角下的部分树
   * @param param
   */
  private async updateResourceObjectMenuPartial(param: UpdateMenuByOperation) {
    const map = {
      pageDesign: 'homeWork',
    };
    const nextType = map[param.menuType] || param.menuType;
    const { code, data } = await this.mdWebapiService
      .getResourceObjectMenuList({
        application: this.appService.selectedApp.code,
        type: nextType,
      })
      .toPromise();
    if (code === 0) {
      const tree = data.businessDirTree;
      this.handleOpenKeys(this.resourceObjectMenu, tree);
      if (tree.length <= 0) {
        // 被删完了，相当于空目录的时候，要隐藏此目录
        const next = this.resourceObjectMenu.filter((e) => {
          if (e.type === nextType) return false;
          return true;
        });
        this._resourceObjectMenu$.next(next);
      } else {
        // 查看目录是不是已存在
        const exists = this.resourceObjectMenu.some((e) => e.type === tree[0]?.type);
        let updateList;
        if (!exists) {
          updateList = [...this.resourceObjectMenu, tree[0]];
        } else {
          updateList = this.resourceObjectMenu.map((e) => {
            if (e.type === tree[0]?.type) return tree[0];
            return e;
          });
        }
        this.handleSortData(updateList || []);
        this._resourceObjectMenu$.next(updateList);
      }
    }
  }

  /**
   * 增删改业务对象刷新路由
   * @param param
   */
  private updateBusinessObjectMenu(param) {
    const { operatorType, navigateUrl } = param;
    this.mdWebapiService.getBusinessObjectMenuList(this.appService.selectedApp.code).subscribe(
      (res) => {
        this.loadingPage = false;
        if (operatorType === 'delete') {
          // 删除业务对象
          this.deleteBusinessMenu(param, res);
        }
        if (operatorType === 'edit') {
          const oldData = this.businessObjectMenu;
          const mergeData = merge(oldData, res?.data);
          this._businessObjectMenu$.next(mergeData);
          this.handleMenuByEdit(param);
          this.handleUpdateBusinessData(param);
        }
        if (operatorType === 'add') {
          const oldData = this.businessObjectMenu;
          const newData = res?.data ?? [];
          this.handleOpenKeys(oldData, newData);
          this._businessObjectMenu$.next(newData);
          if (navigateUrl) {
            this.handleRouterLinkTo(navigateUrl);
          }
        }
      },
      (err) => {
        console.error(err);
      },
    );
  }

  private handleOpenKeys(oldData: any[], newData: any[]) {
    const map = new Map<string, boolean>();
    const recursion = (list: any[], type: 'get' | 'set') => {
      list.forEach((item) => {
        const key = `${item.type}_${item.businessCode}`;
        if (type === 'get') map.set(key, item.open);
        else item.open = map.get(key);
        recursion(item.businessDirTree || [], type);
      });
    };
    recursion(oldData, 'get');
    recursion(newData, 'set');
  }

  /**
   * 增删改'dataView' | 'pageDesign' | 'event' | 'businessProcess'
   * @param param
   */
  updateBusinessObjectSubmenu(param) {
    const { businessCode, menuType, operatorType, deleteId, navigateUrl } = param;
    const searchParams = {
      businessCode,
      type: menuType,
      application: this.appService.selectedApp.code,
    };
    const len = this.businessObjectMenu.length;
    if (!businessCode) {
      searchParams.businessCode = this.businessObjectMenu[len - 1]?.businessCode;
      param.businessCode = searchParams.businessCode;
    }
    this.mdWebapiService.refreshBusinessObjectMenu(searchParams).subscribe(
      (res) => {
        this.menuRefreshMenu(param, res);
        if (operatorType === 'delete') {
          this.deleteSubMenu(param);
        }
        if (operatorType === 'edit') {
          this.handleMenuByEdit(param);
        }
        if (operatorType === 'add' && navigateUrl) {
          // 有url时 重定向 navigateUrl
          this.handleRouterLinkTo(navigateUrl);
        }
      },
      (err) => {
        console.error(err);
      },
    );
  }

  /**
   * 新增后刷新菜单和重定向
   * @param param
   * @param res
   */
  menuRefreshMenu(param, res) {
    const { menuType, businessCode } = param;
    const freshData = res.data?.businessDirTree ?? [];
    const oldData = cloneDeep(this.businessObjectMenu);
    const menuItem = oldData.find((item) => item.businessCode == businessCode);
    if (menuType === 'pageDesign') {
      // 处理其他作业的open
      menuItem.businessDirTree = menuItem.businessDirTree.map((e) => {
        if (e.type === 'homeWork') return freshData;
        return e;
      });
    } else {
      // 新增数据视图/流程/ 作业
      if (freshData.length <= 0) {
        // 数据被删完了，要隐藏此目录
        menuItem.businessDirTree = menuItem.businessDirTree.filter((e) => e.type !== menuType);
      } else {
        const subItem = menuItem.businessDirTree?.find((node) => node.type === menuType);
        if (subItem) {
          subItem.businessDirTree = freshData;
        } else {
          menuItem.businessDirTree.push(res.data);
        }
      }
    }
    // 如果没有子树的时候，需要将这个业务对象隐藏
    const next = oldData.filter((e) => !!e.businessDirTree?.length);
    next.forEach((item) => {
      this.handleSortData(item.businessDirTree);
    });
    this._businessObjectMenu$.next(next);
  }

  /**
   * 数据排序
   * @param businessDirTree
   */
  private handleSortData(businessDirTree: any[]): void {
    const map = new Map([
      ['modelDesign', 1],
      ['dataView', 2],
      ['homeWork', 3],
      ['event', 4],
      ['service', 5],
      ['businessProcess', 6],
      ['detect', 7],
    ]);
    businessDirTree.sort((pre, next) => {
      return map.get(pre.type) > map.get(next.type) ? 1 : -1;
    });
  }

  /**
   * 当业务对象变化时，需要更新数据
   */
  handleUpdateBusinessData(param) {
    this.isUpdateBusinessData$.next(param || false);
  }

  /**
   * 删除业务对象
   */
  deleteBusinessMenu(param, res) {
    const { businessCode } = param;
    let oldData = this.businessObjectMenu;
    // 前端删除
    oldData = oldData.filter((item) => item.businessCode !== businessCode); // 手动过滤掉业务对象
    const mergeData = merge(oldData, res?.data);
    this._businessObjectMenu$.next(mergeData);

    // 处理路由跳转
    const {
      paramMap: {
        params: { businessObjectId, id },
      },
      _routerState: { url },
    } = this.subRouteData;
    // 当前激活的业务对象 ===  删除菜单所属的业务对象
    if (businessObjectId === businessCode) {
      const menuData = this.businessObjectMenu || [];
      if (menuData?.length > 0) {
        this.handleLinkToFirstModel();
      } else {
        this.router.navigate([`/app/business-constructor`], {
          queryParams: {
            appCode: this.appService?.selectedApp?.code,
          },
        });
      }
    }
  }

  /**
   *  删除 视图/作业/事件/服务/流程
   * @param param
   * @param res
   */
  deleteSubMenu(param) {
    const { businessCode, deleteId } = param;
    // 处理路由跳转
    const {
      paramMap: {
        params: { businessObjectId, id },
      },
      _routerState: { url },
    } = this.subRouteData;
    // 当前激活的业务对象 ===  删除菜单所属的业务对象
    if (businessObjectId === businessCode) {
      const isSubMenu = this.menuOpenEum.find((item) => url.indexOf(item.key) > -1);
      if (!isEmpty(isSubMenu) && id === deleteId) {
        // 刚好删除当前激活的路由类型 && 子路由id === 删除的子路由
        this.handleLinkToFirstModel();
      }
    }
  }

  /**
   * 更新菜单
   * dataView' | 'pageDesign' | 'businessProcess' ｜ 'event'
   */
  handleMenuByEdit(param) {
    const { businessCode, menuType, editId } = param;
    const {
      paramMap: {
        params: { businessObjectId, id, pageType },
      },
      _routerState: { url },
    } = this.subRouteData;
    if (businessObjectId === businessCode) {
      this.handleCrumbs({
        businessObjectId,
        id,
        pageType,
      });
      const subMenuType = this.menuOpenEum.find((item) => url.indexOf(item.key) > -1)?.type;
      if (menuType === subMenuType && editId === id) {
        // url类型和操作类型一致 && 操作的ID和当前url渲染的ID一样
        const refresh = `${menuType}Refresh$`;
        this[refresh].next(true);
      }
      if (menuType === 'dataView' && subMenuType === 'pageDesign') {
        // 操作数据视图，当前展示作业页面
        this.pageDesignRefresh$.next({
          dataViewCode: editId,
        });
      }
    }
  }

  /**
   * 检查业务对象页面路由
   * 如果有无业务对象 展示空白页面 如果有业务对象 重定向到第一个业务的模型设计页面
   * @param state
   * @param route
   * @returns
   */
  handleActiveSubMenu(route) {
    if (this.appService.mdLastActiveUrl) {
      this.redirectionUrl = this.appService.mdLastActiveUrl;
      return this.appService.mdLastActiveUrl;
    }
    let redirectionUrl;
    const menuData = this.businessObjectMenu || [];
    const {
      _routerState: { url },
    } = route;
    const regex = /^\/app\/business-constructor\?appCode=/;
    if (regex.test(url) && menuData?.length > 0) {
      redirectionUrl = this.handleLinkToFirstModel(false);
    }
    this.redirectionUrl = redirectionUrl;
    return redirectionUrl;
  }

  handleActiveMenuByRoute(route) {
    if (route) {
      this.handleMenuOpen(this.subRouteData);
    }
  }

  /**
   * 处理一级 二级路由展开问题
   * @param state
   * @param menuData
   * @param route  路由快照
   */
  handleMenuOpen(route) {
    const menuData = this.businessObjectMenu || [];
    const {
      params: { businessObjectId, id },
    } = route.paramMap;
    const {
      _routerState: { url },
    } = route;
    const subMenuType = [
      {
        key: 'model-design',
        type: 'modelDesign',
      },
      ...this.menuOpenEum,
    ].find((item) => url.indexOf(item.key) > -1)?.type;
    menuData.forEach((item) => {
      if (item.businessCode === businessObjectId) {
        item.open = true;
        if (subMenuType) {
          if (subMenuType === 'pageDesign') {
            const homeWork = item.businessDirTree?.find((node) => node.type === 'homeWork') || {};
            homeWork.open = true;
            // 作业会有很多条，单独处理
            const subItem =
              homeWork.businessDirTree?.find((node) => node.type === subMenuType && id === node.businessSubCode) || {};
            subItem.open = true;
          } else {
            const subItem = item.businessDirTree?.find((node) => node.type === subMenuType) || {};
            subItem.open = true;
          }
        }
      }
    });
    this._businessObjectMenu$.next(menuData);
  }

  /**
   * 处理仪表盘的增改
   * @param param
   * @param flag
   * @returns
   */
  HandleAddBasicReport(param: any, flag: any) {
    return new Observable((subscriber) => {
      this.mdWebapiService.addBasicReport(param, flag).subscribe(
        (res) => {
          if (res?.code === 0) {
            this.updateInstrumentPaneMenu().subscribe(() => {
              subscriber.next(res.data);
            });
            return;
          }
          subscriber.next('');
        },
        (err) => {
          subscriber.error(err);
        },
      );
    });
  }

  /**
   * 更新仪表盘菜单
   * @param appCode
   * @returns1
   */
  updateInstrumentPaneMenu() {
    return new Observable((subscriber) => {
      this.mdWebapiService.getInstrumentPaneMenuList(this.appService.selectedApp.code).subscribe(
        (data) => {
          this._instrumentPaneMenu$.next(data?.data);
          subscriber.next(data.data);
        },
        (err) => {
          subscriber.error(err);
        },
      );
    });
  }

  /**
   * 删除报表
   * @param menuItem
   */
  handleDeleteReport(menuItem) {
    const param = `?pattern=STATEMENT&code=${menuItem.code}&resCode=${menuItem.resCode}`;
    this.mdWebapiService.deleteBasicReport(param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-删除成功！'));
          this.updateInstrumentPaneMenu().subscribe(() => {
            this.handleInstrumentPaneMenu(menuItem.code);
          });
        }
      },
      () => {},
    );
  }
  /**
   * 删除表报后刷新报表菜单
   * @param deleteId
   */
  handleInstrumentPaneMenu(deleteId) {
    const {
      paramMap: {
        params: { id },
      },
    } = this.subRouteData;
    // 当前激活的业务对象 ===  删除菜单所属的业务对象
    if (id === deleteId) {
      const menuData = this._instrumentPaneMenu$.getValue() || [];
      if (menuData?.length > 0) {
        this.router.navigate([`/app/business-constructor/${menuData[0]?.path}`], {
          queryParams: {
            appCode: this.appService?.selectedApp?.code,
          },
        });
      } else {
        const bOMenuData = this.businessObjectMenu || [];
        if (bOMenuData?.length > 0) {
          this.handleLinkToFirstModel();
        } else {
          this.router.navigate([`/app/business-constructor`], {
            queryParams: {
              appCode: this.appService?.selectedApp?.code,
            },
          });
        }
      }
    }
  }

  /**
   * 处理面包屑
   * @param params
   * @returns
   */
  handleCrumbs(params) {
    const { businessObjectId, id, pageType } = params;
    const { url } = this.router;
    const crumbs = [];
    const menu = this.businessObjectMenu || [];
    const menuItem = menu.find((item) => item.businessCode === businessObjectId);
    const currentLang = this.languageService?.currentLanguage || 'zh_CN';
    if (isEmpty(menuItem)) {
      this.crumbs = [];
      return;
    }
    crumbs.push(menuItem.lang?.name?.[currentLang] || menuItem.businessName);
    const menuOpenEum = [
      {
        key: 'model-design',
        type: 'model',
      },
      ...this.menuOpenEum,
    ];
    const subMenuType = menuOpenEum.find((item) => url.indexOf(item.key) > -1)?.type;
    this.currentActiveMenuType = subMenuType;
    if (subMenuType === 'pageDesign') {
      const homeWork = menuItem.businessDirTree?.find((node) => node.type === 'homeWork');
      // 作业会有很多条，单独处理
      const subItem =
        homeWork?.businessDirTree?.find((node) => node.type === subMenuType && id === node.businessSubCode) || {};
      crumbs.push(
        subItem?.lang?.name?.[currentLang] ||
          subItem?.lang?.businessSubName?.[currentLang] ||
          subItem?.lang?.modelName?.[currentLang] ||
          subItem?.businessSubName,
      );
      const thirdItem = subItem.businessDirTree?.find((node) => node.type === pageType);
      crumbs.push(
        thirdItem?.lang?.name?.[currentLang] ||
          thirdItem?.lang?.businessSubName?.[currentLang] ||
          thirdItem?.lang?.modelName?.[currentLang] ||
          thirdItem?.businessSubName,
      );
    } else {
      const queryKey = subMenuType === 'model' ? 'modelDesign' : subMenuType;
      const subItem = menuItem.businessDirTree?.find((node) => node.type === queryKey) || {};
      if (queryKey === 'businessProcess') {
        // 业务流程 重命名了，后端不愿意刷数据，暂时前端处理
        crumbs.push(this.translateService.instant('dj-业务流'));
      } else {
        crumbs.push(
          subItem.lang?.name?.[currentLang] ||
            subItem.lang?.businessSubName?.[currentLang] ||
            subItem.lang?.modelName?.[currentLang] ||
            subItem.businessSubName,
        );
      }
      // 数据视图/事件/流程
      const thirdItem = subItem?.businessDirTree?.find((n) => n.businessSubCode === id);
      crumbs.push(
        thirdItem?.lang?.name?.[currentLang] ||
          thirdItem?.lang?.businessSubName?.[currentLang] ||
          thirdItem.lang?.modelName?.[currentLang] ||
          thirdItem.businessSubName,
      );
    }
    this.crumbs = crumbs;
  }

  /**
   * 获取当前业务对象
   * @param params
   * @returns
   */
  getCurrentBusinessServiceCode(params) {
    const { businessObjectId, id, serviceCode } = params;
    const menu = this.businessObjectMenu || [];
    const menuItem = menu.find((item) => item.businessCode === businessObjectId && item.modelId === id);
    if (menuItem) return Object.assign({}, menuItem, { serviceCode });
    const businessObj = menu.find((item) => item.businessCode === businessObjectId);
    if (businessObj) {
      return Object.assign({}, businessObj, { modelId: id, serviceCode });
    }
    return {};
  }

  /**
   * 通过业务代号 businessCode 获取当前业务对象
   * @param params
   * @returns
   */
  getCurrentBusinessByBusinessCode(params) {
    const { businessObjectId } = params;
    const menu = this.businessObjectMenu || [];
    const menuItem = menu.find((item) => item.businessCode === businessObjectId);
    return menuItem || {};
  }

  /**
   * 作业 切换PC 移动图标
   */
  onExchangePageDesignType(type?: 'mobile' | 'pc') {
    const pageDesignType = !!type ? type : this._exchangePageDesignType$.getValue() === 'pc' ? 'mobile' : 'pc';
    this._exchangePageDesignType$.next(pageDesignType);
  }

  setContentSaveLoading(value: boolean) {
    this._contentSaveLoading = value;
  }

  resetContentChangeCheckObject() {
    this._contentChangeCheckObject = null;
  }

  setContentChangeCheckObject(checkObject: CheckObject = null, isContentChangeWithoutSave: boolean = null) {
    this._contentChangeCheckObject = { checkObject, isContentChangeWithoutSave };
  }

  getIsContentChangeWithoutSave() {
    const { checkObject, isContentChangeWithoutSave } = this._contentChangeCheckObject || {};
    return isContentChangeWithoutSave ?? checkObject?.contentComponent?.[checkObject?.checkFunction]?.() ?? false;
  }

  /**
   * 是不是对象视角
   */
  get businessObjectPerspective() {
    return this.tabIndex === 0;
  }

  getAddSourcetype() {
    return {
      addSourceType: !this.businessObjectPerspective
        ? EPerspective.RESOURCEPERSPECTIVE
        : EPerspective.OBJECTPERSPECTIVE,
    };
  }

  /**
   * 自动跳转至第一个业务对象的模型
   */
  private handleLinkToFirstModel(redirect: boolean = true): string {
    const menuData = this.businessObjectMenu || [];
    let oldData = menuData[0];
    while (oldData?.businessDirTree?.length) {
      oldData = oldData.businessDirTree[0];
    }
    const path = oldData?.path;
    const redirectionUrl: string = `/app/business-constructor/${path}?appCode=${this.appService.selectedApp.code}`;
    if (redirect) {
      this.router.navigateByUrl(redirectionUrl);
    }
    return redirectionUrl;
  }

  /**
   * 根据路由打开资源视角的树
   */
  public updateResourcePerspectiveExpandKeys(path: string): void {
    const nextPath = path.replace('/app/business-constructor/', '').split('?')[0];
    const [padType] = nextPath.split('/');
    const menuOpenEum = [
      {
        key: 'model-design',
        type: 'modelDesign',
      },
      {
        key: 'page-design',
        type: 'homeWork',
      },
      ...this.menuOpenEum,
    ];
    const type = menuOpenEum.find((e) => e.key === padType);
    if (!type) return;

    const nextData = this.handleResourceData(this.resourceObjectMenu, nextPath);
    this._resourceObjectMenu$.next(nextData);
  }

  /**
   * 找出指定路由的数据路径
   * @param curKey
   * @param data
   * @param result
   * @returns
   */
  private getPathByKey = (curKey: string, data: any, result: any) => {
    const traverse = (curKey, path, data) => {
      if (data.length === 0) {
        return;
      }
      for (let item of data) {
        path.push(item);
        if (item.path === curKey) {
          if (path.length) {
            path.pop();
            result.push(...path);
          }
          return;
        }
        const businessDirTree = item.businessDirTree || [];
        traverse(curKey, path, businessDirTree);
        path.pop();
      }
    };
    traverse(curKey, [], data);
    return result;
  };

  private handleResourceData(data: any, nextPath: string): void {
    const cloneData = cloneDeep(data);
    let result = [];
    this.getPathByKey(nextPath, cloneData, result);
    result.forEach((item) => {
      if (!item.open) item.open = true;
    });
    return cloneData;
  }
}
