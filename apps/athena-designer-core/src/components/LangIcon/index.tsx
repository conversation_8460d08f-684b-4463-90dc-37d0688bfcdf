import React, {
  Fragment,
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from 'react';
import { useTranslation } from 'react-i18next';
import { Form } from 'antd';
import AppDialog from '../AppDialog';
import AppInput from '../AppInput';
import useLoading from '@/pages/DataCenter/pages/DropdownVocabulary/hooks/useLoading';
import {
  SupportedLang,
  getCurrentLangConf,
  getChangeLanguageCondition,
  ChangeLanguageCondition,
} from './lang-tools';
import useAxios from '@/pages/DataCenter/pages/DropdownVocabulary/hooks/useAxios';
import useDebounce from '@/pages/DataCenter/pages/DropdownVocabulary/hooks/useDebounce';
// import serviceConfig from '@/pages/DataCenter/pages/DropdownVocabulary/tools';

import './index.less';
import { CommonAction } from '@/common/types';

import type { ChangeEvent } from 'react';

const FormItem = Form.Item;

/**
 * INFO:兼容一下本地开发
 * webpack配置里面有关于img的配置,原先css的hover操作会造成webpack去找资源,而资源在主解决方案中,显然找不到,所以这么做兼容一下本地开发
 */
const LangIcon = forwardRef(function <T extends DV.AnyOBJ>(
  props: DV.LangIconProps<T>,
  ref: LangIcon.LangRefType
) {
  const { callback, id, title, syncData, classNames = {} } = props;

  const [hover, setHover] = useState(false);
  const [isShow, setShow] = useState(false);
  const [currentData, setCurrentData] = useState(null);
  const modifyInfoRef = useRef({
    zh_CN: true,
    zh_TW: true,
    en_US: true,
  });
  const [translateInfo, setTranslateInfo] = useState<LangIcon.TranslateInfoType>({});
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { data, fetch } = useAxios({
    url: '/athena-designer/translate/translate',
  });

  useImperativeHandle(ref, () => {
    return {
      valueChange: doValueChangeHandler,
      translate: async (content: string, convertTypes: string[]) => {
        const data = await fetch({ content, convertTypes });
        return data?.data;
      },
    };
  });

  if (props.useTranslate) {
    useDebounce({
      callback: async ({ content, convertTypes }) => {
        if (content && convertTypes) {
          fetch({ content, convertTypes: [convertTypes] });
        }
      },
      dependencies: [translateInfo],
      delay: 300,
    });
  }

  useEffect(() => {
    if (data?.data) {
      const { content, setResponse, setOrigin, convertTypes } = translateInfo;
      const info = {};
      if (setResponse?.length > 0) {
        setResponse.map((name: string) => {
          info[name] = content ? data?.data?.[convertTypes] : '';
        });
      }
      if (setOrigin?.length > 0) {
        setOrigin.map((name: string) => {
          info[name] = content;
        });
      }
      form.setFieldsValue(info);
      const finalInfo = {
        ...currentData,
        ...info,
      };
      setCurrentData(finalInfo);
      if (!isShow && syncData) {
        syncData(finalInfo);
      }
    }
  }, [data, translateInfo]);

  useEffect(() => {
    setCurrentData({ ...props.data });
    form.setFieldsValue({
      zh_CN: props.data?.['zh_CN'],
      zh_TW: props.data?.['zh_TW'],
      en_US: props.data?.['en_US'],
    });
  }, [props.data]);

  const NormalIcon = () => {
    return <img src="/assets/img/designer/multiLang.svg" />;
  };

  const HoverIcon = () => {
    return <img src="/assets/img/designer/multiLangColor.svg" />;
  };

  useEffect(() => {
    props.syncStatus?.(isShow);
  }, [isShow]);

  const doShowLangModal = () => {
    setShow(true);
  };

  const langConf = getCurrentLangConf(t('dj-LANG'));

  const doHideDialog = () => {
    setShow(false);
  };

  const { loading: okLoading, fn: doOk } = useLoading(async () => {
    try {
      const res: T = await form.validateFields();
      callback({
        type: CommonAction.OK,
        data: res,
        id,
      });
      setShow(false);
    } catch (error) {
      console.log(error);
    }
  }, [form]);

  const doValueChange = (event: ChangeEvent<HTMLInputElement>, language: SupportedLang) => {
    if (!isShow) return;
    doValueChangeHandler(event.target.value?.trim(), language);
  };

  const doValueChangeHandler = (value: string, language: SupportedLang) => {
    const currentLanguage = t('dj-LANG') as SupportedLang;
    const condition = getChangeLanguageCondition(currentLanguage, language);
    const translatedLang = language === SupportedLang.CN ? SupportedLang.TW : SupportedLang.CN;
    const convertTypes = language === SupportedLang.CN ? 'zh2Hant' : 'zh2Hans';
    const originModify = [language];
    switch (condition) {
      case ChangeLanguageCondition.CN_CN:
      case ChangeLanguageCondition.TW_TW:
        if (modifyInfoRef.current[SupportedLang.US]) {
          originModify.push(SupportedLang.US);
        }
        setTranslateInfo({
          content: value,
          convertTypes: convertTypes,
          setResponse: [translatedLang],
          setOrigin: originModify,
        });
        break;
      case ChangeLanguageCondition.CN_EN:
        modifyInfoRef.current[SupportedLang.US] =
          value === '' || value === currentData[SupportedLang.CN];
        break;
      case ChangeLanguageCondition.TW_EN:
        modifyInfoRef.current[SupportedLang.US] =
          value === '' || value === currentData[SupportedLang.TW];
        break;
      case ChangeLanguageCondition.EN_EN:
        if (modifyInfoRef.current[SupportedLang.CN]) {
          form.setFieldValue(SupportedLang.CN, value);
          setCurrentData({
            ...currentData,
            [SupportedLang.CN]: value,
          });
        }
        if (modifyInfoRef.current[SupportedLang.TW]) {
          form.setFieldValue(SupportedLang.TW, value);
          setCurrentData({
            ...currentData,
            [SupportedLang.TW]: value,
          });
        }
        break;
      case ChangeLanguageCondition.EN_CN:
        modifyInfoRef.current[SupportedLang.CN] =
          value === '' || value === currentData[SupportedLang.US];
        break;
      case ChangeLanguageCondition.EN_TW:
        modifyInfoRef.current[SupportedLang.TW] =
          value === '' || value === currentData[SupportedLang.US];
        break;
    }
    let allEmpty = true;
    Object.keys(currentData).map((key) => {
      if (currentData[key]) {
        allEmpty = false;
      }
    });
    if (allEmpty) {
      modifyInfoRef.current = {
        [SupportedLang.CN]: true,
        [SupportedLang.TW]: true,
        [SupportedLang.US]: true,
      };
    }
  };

  // 处理图标闪烁问题
  const opIcon = useMemo(() => {
    return (
      <span
        onClick={doShowLangModal}
        style={{ display: 'inline-flex', cursor: 'pointer' }}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
      >
        {!hover ? <NormalIcon /> : <HoverIcon />}
      </span>
    );
  }, [hover]);

  return (
    <Fragment>
      {opIcon}
      <AppDialog
        className="langModalInput"
        classNames={classNames}
        width={'282px'}
        okText={t('dj-确定')}
        cancelText={t('dj-取消')}
        title={`${title}${t('dj-多语言设定')}`}
        onCancel={doHideDialog}
        okButtonProps={{ loading: okLoading }}
        onOk={doOk}
        open={isShow}
      >
        <Form name="iconForm" initialValues={currentData} form={form}>
          {langConf.map((langInfo) => {
            return (
              <FormItem<DV.SupportedLangValue>
                labelCol={{ span: 0 }}
                key={langInfo.formControlName}
                rules={[
                  {
                    required: true,
                    message: t(langInfo.label),
                  },
                ]}
                name={langInfo.formControlName}
              >
                <AppInput
                  animateLabel
                  required
                  isTextArea={props.isTextArea}
                  disabled={props.useTranslate ? langInfo.readonly : false}
                  style={{ width: '234px' }}
                  onChange={(event) => doValueChange(event, langInfo.formControlName)}
                  manualClassLists={['langModalInput']}
                  placeholder={t(langInfo.label)}
                />
              </FormItem>
            );
          })}
        </Form>
      </AppDialog>
    </Fragment>
  );
});

export default LangIcon;
