/** @jsxRuntime classic */
/** @jsx jsxCustomEvent */
import jsxCustomEvent from '@micro-zoe/micro-app/polyfill/jsx-custom-event';
import React, { useMemo } from 'react';
import './index.less';
import { MessageToSubType, MessageToSubAction, ActionSideBarData, PanelType } from './type';
import {
  useDynamicWorkDesignContentStore,
  useDynamicWorkDesignStore,
  useDynamicWorkDesignSideBarStore,
} from '../../store';
import { globalEventEmitter, useGlobalEventEmitter } from '@/common/hooks';
import {
  DynamicWorkDesignEventType,
  DynamicWorkDesignStatus,
  PageUIElementContent,
} from '../../config/type';
import { appName, url, currentLanguage } from './athMicroApp';

import useHandleMicroAppDataChange from './hooks/useHandleMicroAppDataChange';
import useSystemConfig from '../../hooks/common/useSystemConfig';
import useMicroAppSetData from './hooks/useMicroAppSetData';
import useLowcodeRenderInitData from './hooks/useLowcodeRenderInitData';
import useIsvPackageData from './hooks/useIsvPackageData';
import { Empty } from 'antd';
import { t } from 'i18next';
import { isEmpty } from 'lodash';
import { TAB_CONFIG } from '../DynamicWorkDesignSideBar/config';
import { AppTypes } from '@/pages/Deployer/components/Publish/utils';

import AppDslWorkSet from '@/components/DslWorkSet';
import { ActionPageUIElementContentData } from '@athena-designer-editor/src/plugins/plugin-ath-loader/type';
// import { getRenderDslWorkDesignBaseData } from '@components/DynamicWorkDesign/utils';
import useDynamicWorkDesignStatus from '@components/DynamicWorkDesign/hooks/business/useDynamicWorkDesignStatus';

export interface DynamicWorkDesignContentProps {}
const DynamicWorkDesignContent: React.FC<DynamicWorkDesignContentProps> = (
  props: DynamicWorkDesignContentProps
) => {
  const { lowcodeRenderInitData } = useDynamicWorkDesignContentStore((state) => ({
    lowcodeRenderInitData: state.lowcodeRenderInitData,
  }));

  const { dynamicWorkDesignRenderData, dynamicWorkDesignInfo } = useDynamicWorkDesignStore(
    (state) => ({
      dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
      dynamicWorkDesignInfo: state.dynamicWorkDesignInfo,
    })
  );

  const { isCustomize, customTip, customButtonTip, customType } =
    dynamicWorkDesignInfo?.dynamicWorkDesignConfig?.commonConfig || {};

  const currentSubmitActions = useMemo(() => {
    return lowcodeRenderInitData?.pageUIElementContent?.submitActions;
  }, [lowcodeRenderInitData]);

  const { systemConfig } = useSystemConfig();
  const { isvComponentList } = useIsvPackageData();

  const { dynamicWorkDesignStatus } = useDynamicWorkDesignStatus();
  // 处理lowcode初始化渲染数据
  useLowcodeRenderInitData(systemConfig, isvComponentList, dynamicWorkDesignStatus);

  // TODO 这块提示逻辑 后续 可以跟随 状态逻辑的 优化 进行优化
  const emptyDescription = useMemo(() => {
    if (DynamicWorkDesignStatus.Loading === dynamicWorkDesignStatus) {
      return t('dj-加载中');
    }

    // if (isEmpty(dynamicWorkDesignRenderData?.dataSources)) {
    //   return t('dj-请先选择数据源');
    // }

    if (!lowcodeRenderInitData) {
      return t('dj-正在组装渲染的数据');
    }

    return '';
  }, [dynamicWorkDesignRenderData, lowcodeRenderInitData, dynamicWorkDesignStatus]);

  const { currentTabType } = useDynamicWorkDesignSideBarStore((state) => ({
    currentTabType: state.currentTabType,
  }));

  // 处理与lowcode的数据设置
  const { sendMessageToEditor } = useMicroAppSetData();
  // 处理lowcode的数据变更
  const { handleDatachange } = useHandleMicroAppDataChange(appName);

  // =======事件处理=======
  useGlobalEventEmitter(
    DynamicWorkDesignEventType.AthSideBarToggleLowCodeTabContent,
    (isShow: boolean, panel?: PanelType, isSideBarRivet?: boolean) => {
      const contentData = {
        isShow,
        isSideBarRivet,
      } as ActionSideBarData;
      panel && (contentData['panel'] = panel);

      sendMessageToEditor({
        type: MessageToSubType.Action,
        data: {
          key: 'sideBar',
          data: contentData,
        },
      } as MessageToSubAction);
    }
  );

  useGlobalEventEmitter(
    DynamicWorkDesignEventType.AthChangePageUIElementContent,
    (pageUIElementContent: Partial<PageUIElementContent>, extraData: any ) => {
      sendMessageToEditor({
        type: MessageToSubType.Action,
        data: {
          key: 'pageUIElementContent',
          data: {
            type: 'update',
            data: pageUIElementContent,
            extraData
          } as ActionPageUIElementContentData,
        }
      } as MessageToSubAction);
    }
  );

  const onSubmitActionsChange = (submitActions: any[]) => {
    globalEventEmitter.emit(DynamicWorkDesignEventType.AthChangePageUIElementContent, {
      submitActions,
    }, { isLoadImmediately: true });
  };

  const styles =
    currentTabType && TAB_CONFIG[currentTabType]?.contentRenderType === 'lowCode'
      ? { width: 'calc(100% - 237px)', left: '237px' }
      : { width: '100%', left: '0' };

  const doOpenSource = () => {
    const { iamToken, tenantId } = (systemConfig as any)?.userInfo ?? {};
    const { applicationCode } = dynamicWorkDesignInfo ?? {};
    // 打开源码，参数携带
    const route = 'manage';
    const vscodeProtocol =
      `@vscodeinit&route=${route}&env=@env&version=@version&fileId=@fileId` +
      `&token=${iamToken}&code=${applicationCode}&type=${customType}`;

    const encodedUrl = encodeURIComponent(vscodeProtocol);
    window.open(`/open-vscode?url=${encodedUrl}`, '_blank');
  };

  return (
    <div className={`dynamic-work-design-content ${emptyDescription ? 'hide-editor' : ''}`}>
      {isCustomize && (
        <div className="custom-wrapper" style={styles}>
          <div className="custom-widget">
            <div className="custom-title">{customTip}</div>
            {dynamicWorkDesignInfo?.appType === AppTypes.MODEL_DRIVEN &&
              systemConfig?.config?.enableOpenVscode && (
                <div className="open-source-title" onClick={doOpenSource}>
                  {customButtonTip}
                </div>
              )}
          </div>
          <AppDslWorkSet
            submitActions={currentSubmitActions}
            dynamicWorkDesignInfo={dynamicWorkDesignInfo}
            onSubmitActionsChange={onSubmitActionsChange}
          ></AppDslWorkSet>
        </div>
      )}

      <micro-app
        class={`athena-designer-editor ${isCustomize ? 'hiddenMicroApp' : ''}`}
        name={appName}
        url={url}
        data={{ currentLanguage }}
        onCreated={() => console.log('micro-app元素被创建')}
        onBeforemount={() => console.log('即将渲染')}
        onMounted={() => console.log('已经渲染完成')}
        onUnmount={() => console.log('已经卸载')}
        onError={() => console.log('加载出错')}
        onDatachange={handleDatachange}
        router-mode="pure"
      ></micro-app>

      <Empty description={emptyDescription} className={'athena-designer-editor-empty'} />
    </div>
  );
};

export default DynamicWorkDesignContent;
