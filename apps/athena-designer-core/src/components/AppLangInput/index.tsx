import React, { useEffect, useState, useRef } from 'react';
import AppInput from '../AppInput';
import LangIcon from '../LangIcon';
import { useTranslation } from 'react-i18next';
import { CommonAction } from '@/common/types';
import useDebounce from '@/pages/DataCenter/pages/DropdownVocabulary/hooks/useDebounce';

import type { ChangeEvent } from 'react';

function AppLangInput(props: AppLangInputType.AppLangInputProps<DV.LangConfig>) {
  const {
    value,
    onChange,
    title = '',
    isTextArea = false,
    onDialogStatusChange,
    classNames = {},
    useTranslate: useTranslateProp = true,
    ...rest
  } = props;

  const useTranslate = useTranslateProp !== false;

  const [inputValue, setInputValue] = useState<DV.LangConfig>(null);
  const [translateValue, setTranslateValue] = useState<string>(null);
  const { t } = useTranslation();
  const langIcongRef = useRef(null);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const callOnChange = (data: DV.LangConfig) => {
    if (onChange) {
      onChange(data);
    }
  };

  const queryTranslate = async (content: string) => {
    if (!useTranslate) return;
    if (!content) return;
    if (langIcongRef.current) {
      const data = await langIcongRef.current.translate(content, ['zh2Hant', 'zh2Hans']);
      if (data) {
        const newValue = {
          en_US: content,
          zh_CN: data?.['zh2Hans'],
          zh_TW: data?.['zh2Hant'],
        };
        setInputValue({ ...newValue });
        callOnChange({ ...newValue });
      }
    }
  };

  console.log('props.useTranslate', props.useTranslate);

  if (useTranslate) {
    useDebounce({
      callback: queryTranslate,
      dependencies: [translateValue],
      delay: 300,
    });
  }

  const doOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.trim();
    let data = {
      ...inputValue,
      [t('dj-LANG')]: value,
    };
    if (!value) {
      data = {
        zh_CN: '',
        en_US: '',
        zh_TW: '',
      };
    }
    setInputValue({ ...data });
    callOnChange({ ...data });
    setTranslateValue(value);
  };

  // 现在外部也需要实时查询，就不需要同步数据了
  // const syncLangData = (data: DV.LangConfig) => {
  //   setInputValue(data);
  //   callOnChange(data);
  // };

  const doLangChange = (data: DV.OpAction<DV.LangConfig>) => {
    if (data.type === CommonAction.OK) {
      const res = data.data;
      setInputValue(res);
      callOnChange(res);
    }
  };

  return (
    <div className="appLangInput">
      <AppInput
        {...rest}
        value={inputValue?.[t('dj-LANG')]}
        isTextArea={isTextArea}
        onChange={doOnChange}
        suffix={
          <LangIcon<DV.LangConfig>
            classNames={classNames}
            title={title}
            ref={langIcongRef}
            data={inputValue}
            useTranslate={useTranslate}
            syncStatus={onDialogStatusChange}
            // syncData={syncLangData}
            callback={doLangChange}
          />
        }
      />
    </div>
  );
}

export default AppLangInput;
