import { useWorkData } from '@/components/OpenwindowWorkDesignLib/store/workData';
import { data } from '@remix-run/router';
import { Button, Checkbox, Input, Modal, Popover } from 'antd';
import { t, use } from 'i18next';
import React, { useEffect, useState } from 'react';
import './index.less';
import { cloneDeep } from 'lodash';
import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesignLib/utils/fields.utils';
import { combinDslColumnDef, combinDslComponent } from '../../config';
import { SearchOutlined } from '@ant-design/icons';

const { Search } = Input;

interface AddColumnConfigurationProps {
  dataConnectorId: string;
}

/**
 * 列配置
 * @param props
 * @returns
 */
const AddColumnConfiguration: React.FC<AddColumnConfigurationProps> = (
  props: AddColumnConfigurationProps
) => {
  const { openWindowDefine, columnDefs, updateColumnDefs, fieldTreeMap } = useWorkData(
    (state) => state
  ); // 列配置
  const [searchColumns, setSearchColumns] = useState<any[]>([]);

  useEffect(() => {
    const columns = cloneDeep(fieldTreeMap()?.get(props.dataConnectorId)?.[0]?.field ?? []);
    onSearch('');
  }, [props.dataConnectorId]);

  const onSearch = (value: string) => {
    // 示例搜索逻辑
    const filtered = cloneDeep(
      (fieldTreeMap()?.get(props.dataConnectorId)?.[0]?.field ?? []).filter((col) => {
        return (
          !['array', 'object'].includes(col.data_type) &&
          (col.data_name.toLowerCase().includes(value.toLowerCase()) ||
            col?.description?.[t('dj-LANG')].includes(value.toLowerCase()))
        );
      })
    );
    setSearchColumns(filtered);
  };

  // 检查列是否已被选中
  const isColumnSelected = (dataName: string) => {
    console.log(
      columnDefs() ?? [],
      (columnDefs() ?? []).some((col: any) => col?.columns?.[0]?.schema === dataName)
    );
    return (columnDefs() ?? []).some((col: any) => col?.columns?.[0]?.schema === dataName);
  };

  const onChange = (e: any, dataName: string, index: number) => {
    // 更新 columnDefs
    const newColumnDefs = cloneDeep(columnDefs());
    if (e.target.checked) {
      const newColumn = (fieldTreeMap()?.get(props.dataConnectorId)?.[0]?.field ?? []).find(
        (field: any) => {
          return field.data_name === dataName;
        }
      );
      // 追加
      const columnDef = combinDslColumnDef(
        combinDslComponent(newColumn, ATHENA_TYPES.INPUT, {
          language: t('dj-LANG'),
        })
      );
      newColumnDefs.push(columnDef);
    } else {
      // 删除
      const index = newColumnDefs.findIndex(
        (columnDef: any) => columnDef?.columns?.[0]?.schema === dataName
      );
      newColumnDefs.splice(index, 1);
    }
    updateColumnDefs(newColumnDefs);
  };

  return (
    <>
      {(fieldTreeMap()?.get(props.dataConnectorId)?.[0]?.field ?? []).length === 0 ? (
        <div
          style={{
            fontSize: '16px',
            cursor: 'not-allowed',
            color: 'rgba(0, 0, 0, 0.25)',
          }}
        >
          +
        </div>
      ) : (
        <Popover
          trigger="click"
          title={null}
          arrow={false}
          content={
            <div className="add-column-figuration" style={{ width: '200px', height: '260px' }}>
              <Search
                allowClear
                placeholder={t('dj-请输入')}
                onSearch={onSearch}
                style={{ width: '100%' }}
              />

              <div style={{ marginTop: '8px', height: '225px', overflowY: 'auto' }}>
                {searchColumns.map((col, i) => (
                  <li className={`list-item`} key={col.id}>
                    <div className="header-name">
                      <Checkbox
                        checked={isColumnSelected(col.data_name)}
                        onChange={(e) => onChange(e, col.data_name, i)}
                      ></Checkbox>
                      <span style={{ marginLeft: '10px' }}>{col?.description?.[t('dj-LANG')]}</span>
                    </div>
                  </li>
                ))}
              </div>
            </div>
          }
        >
          <div
            style={{
              fontSize: '16px',
              color: '#605CE5',
            }}
          >
            +
          </div>
        </Popover>
      )}
    </>
  );
};

export default AddColumnConfiguration;
