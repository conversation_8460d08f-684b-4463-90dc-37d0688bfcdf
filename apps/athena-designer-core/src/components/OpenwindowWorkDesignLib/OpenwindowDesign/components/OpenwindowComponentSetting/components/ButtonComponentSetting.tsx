import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
// import './index.less';
import { t } from 'i18next';
import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesignLib/utils/fields.utils';
import { Checkbox, Form, Input, InputNumber, Select } from 'antd';
import { dslI18n, getComponentList } from '../../../config';
import AppLangInput from '@/components/AppLangInput';
import { cloneDeep, debounce } from 'lodash';
import { useWorkData } from '@/components/OpenwindowWorkDesignLib/store/workData';

const { Option } = Select;

interface ButtonComponentSettingProps {
  data: any;
  //   columnType: string;
  change: (data: any) => void;
}

const ButtonComponentSetting = (props: ButtonComponentSettingProps) => {
  const athTypeOptions: string[] = [
    'primary',
    'default',
    'text',
    'link',
    'cell-link',
    'link-with-bg',
    'icon',
    'dashed',
  ];
  const sizeTypeOptions: string[] = ['large', 'default', 'small'];
  const alignOptions: { label: string; value: string }[] = [
    { label: '左对齐', value: 'left' },
    { label: '居中', value: 'center' },
    { label: '右对齐', value: 'right' },
  ];

  const isInitialized = useRef(false);
  const [dataTypeOptions, setDataTypeOptions] = useState<string[]>([]); // 数据类型
  const [componentForm] = Form.useForm(); // 表单 [文本输入、日期选择、时间选择、数字输入、百分比]
  useEffect(() => {
    console.log('2222', props.data);
    handleInit();
  }, [props.data]);

  const { openWindowDefine, updateOpenWindowDefine, columnType, activeData } = useWorkData(
    (state) => state
  );

  /**
   * 初始化
   */
  const handleInit = () => {
    const {
      type,
      id,
      title,
      schema,
      path,
      editable,
      athType,
      sizeType,
      debounce,
      debounceTime,
      beforeIcon,
      afterIcon,
      align,
      readonly,
      dataType,
      lang,
    } = props.data;
    setDataTypeOptions(getComponentList(type, dataType));
    const status = editable ? 'editable' : 'disabled';
    componentForm.setFieldsValue({
      id, // 唯一标识
      title: lang?.title ?? dslI18n, // 标题
      schema, // schema
      path, // path
      status, // 状态
      // 按钮类型
      athType,
      // 按钮大小
      sizeType,
      // 防抖是否开启
      debounce,
      // 防抖时间
      debounceTime,
      // 前缀Icon
      beforeIcon,
      // 后缀Icon
      afterIcon,
      // 对齐位置
      align,
      // noSubmit是否开启
      readonly,
      dataType, // 数据类型
    });
  };

  /**
   * 多语言输入框回调
   * @param key
   * @param data
   */
  const handlePatchLang = (key: any, data: any) => {
    if (['title'].includes(key)) {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
        ['label']: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    } else {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    }
    onChange();
  };

  // 创建防抖函数并保持引用不变
  const debouncedSearch = useCallback(
    debounce((key, data) => handlePatchLang(key, data), 500),
    [] // 依赖项为空，确保防抖函数只创建一次
  );

  // 输入框变化事件
  const handleChange = (key: any, data: any) => {
    debouncedSearch(key, data); // 触发防抖函数
  };

  /**
   * 数据类型变更
   * @param e
   */
  const handleChangeDataType = (e) => {
    componentForm.setFieldsValue({ dataType: e });
    onChange();
  };

  /**
   * 值更新
   * @param key
   * @param value
   */
  const handleChangeAttr = (key, value) => {
    componentForm.setFieldsValue({ [key]: value });
    onChange();
  };

  /**
   * 回填
   */
  const onChange = () => {
    console.log(componentForm.getFieldsValue());
    const {
      title,
      status,
      athType,
      sizeType,
      debounce,
      debounceTime,
      beforeIcon,
      afterIcon,
      align,
      readonly,
      dataType,
    } = componentForm.getFieldsValue();
    const editable = status === 'editable';
    const disabled = !editable;
    let data = {
      ...props.data,
      title: title[t('dj-LANG')],
      label: title[t('dj-LANG')],
      lang: {
        title,
        label: title,
      },
      editable,
      disabled,
      athType,
      sizeType,
      debounce,
      debounceTime,
      beforeIcon,
      afterIcon,
      align,
      readonly,
      dataType,
    };
    props.change({
      isControl: true,
      value: data,
    });
  };

  return (
    <div className="component-form">
      <Form
        className="form-info"
        form={componentForm}
        name="openwindow-setting-form"
        layout="vertical"
        labelCol={{
          span: 24,
        }}
        wrapperCol={{
          span: 24,
        }}
      >
        {/* 唯一标识 */}
        <Form.Item label={t('dj-唯一标识')} name="id">
          <Input disabled />
        </Form.Item>
        {/* 标题 */}
        <Form.Item label={t('dj-标题')} name="title">
          <AppLangInput
            required
            size="small"
            useTranslate={false}
            onChange={(value) => handleChange('title', value)}
          />
        </Form.Item>
        {/* schema */}
        <Form.Item label="schema" name="schema">
          <Input disabled />
        </Form.Item>
        {/* path */}
        <Form.Item label="path" name="path">
          <Input disabled />
        </Form.Item>
        {/* 状态 */}
        <Form.Item label={t('dj-状态')} name="status">
          <Select disabled>
            <Select.Option value="editable">{t('普通')}</Select.Option>
            <Select.Option value="disabled">{t('禁用')}</Select.Option>
          </Select>
        </Form.Item>
        {/* 按钮类型 */}
        <Form.Item label={t('dj-按钮类型')} name="athType">
          <Select onChange={(e) => handleChangeAttr('athType', e)}>
            {athTypeOptions.map((option) => {
              return <Select.Option value={option}>{option}</Select.Option>;
            })}
          </Select>
        </Form.Item>
        {/* 按钮大小 */}
        <Form.Item label={t('dj-按钮大小')} name="sizeType">
          <Select onChange={(e) => handleChangeAttr('sizeType', e)}>
            {sizeTypeOptions.map((option) => {
              return <Select.Option value={option}>{option}</Select.Option>;
            })}
          </Select>
        </Form.Item>
        {/* 防抖是否开启 */}
        <Form.Item label={t('dj-防抖')} name="debounce" valuePropName="checked">
          <Checkbox onChange={(e) => handleChangeAttr('debounce', e.target.checked)}>
            {t('dj-是否开启')}
          </Checkbox>
        </Form.Item>
        {/* 防抖时间 */}
        <Form.Item label={t('dj-防抖时间')} name="debounceTime">
          <InputNumber
            defaultValue={props.data.debounceTime}
            value={componentForm.getFieldValue('debounceTime')}
            onChange={(value) => handleChangeAttr('debounceTime', value)}
          />
        </Form.Item>
        {/* 前缀Icon */}
        <Form.Item label={t('dj-前缀Icon')} name="beforeIcon">
          <Input onChange={(e) => handleChangeAttr('beforeIcon', e.target.value)} />
        </Form.Item>
        {/* 后缀Icon */}
        <Form.Item label={t('dj-后缀Icon')} name="afterIcon">
          <Input onChange={(e) => handleChangeAttr('afterIcon', e.target.value)} />
        </Form.Item>
        {/* 对齐位置 */}
        <Form.Item className="prop-select" label={t('dj-对齐位置')} name="align">
          <Select onChange={(e) => handleChangeAttr('align', e)}>
            {alignOptions.map((option) => {
              return <Select.Option value={option.value}>{option.label}</Select.Option>;
            })}
          </Select>
        </Form.Item>
        {/* noSubmit是否开启 */}
        <Form.Item label="noSubmit" name="readonly" valuePropName="checked">
          <Checkbox onChange={(e) => handleChangeAttr('readonly', e.target.checked)}>
            {t('dj-是否开启')}
          </Checkbox>
        </Form.Item>
        {/* 数据类型 */}
        <Form.Item label={t('dj-数据类型')} name="dataType">
          <Select onChange={(e) => handleChangeDataType(e)}>
            {dataTypeOptions.map((option) => {
              return <Select.Option value={option}>{option}</Select.Option>;
            })}
          </Select>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ButtonComponentSetting;
