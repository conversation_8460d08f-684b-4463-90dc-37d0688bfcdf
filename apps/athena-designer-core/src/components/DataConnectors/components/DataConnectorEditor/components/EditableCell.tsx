import React, { useRef } from 'react';
import { Input, Select,Tooltip } from 'antd';
import './EditableCell.less'
import { useTranslation } from 'react-i18next';
import VariableSelect from '@/components/DataVariablesSelect'
import { insertTextAtCursor } from '@/components/DataConnectors/utils/cursorUtils';

export const EditableCellInput = ({ value, onChange, editing, onClickCell, showSuffix, ...rest }) => {
    const {t} = useTranslation()
    const inputRef = useRef<any>(null);
    
    return (
        <Input 
            ref={inputRef}
            placeholder={t('dj-请输入')}
            className={`drag-input-item editable-cell-input${(editing) ? ' editing' : ''}`}
            value={value}
            onChange={e => {
                if (editing) onChange(e.target.value);
            }}
            onBlur={() => {rest?.onBlurkCell && rest.onBlurkCell()}}
            readOnly={!editing}
            onClick={() => {
                if (!editing && onClickCell) onClickCell();
            }}
                
            suffix={
                showSuffix && 
                <Tooltip title={t('dj-添加变量')}>
                    <VariableSelect onSelect={(varName) => {
                        console.log('todo添加变量--varName', varName)
                        const newValue = insertTextAtCursor(value || '', varName, inputRef);
                        onChange?.(newValue);
                    }} />
                  
                </Tooltip>
            }
           
            {...rest}
        />
    );
};
export const EditableSelect = ({ value, onChange, editing, onClickCell,options , ...rest }) => {
    const {t} = useTranslation()
    return (
        <Select  placeholder={t('dj-请输入')}
            className={`drag-input-item editable-select-cell${(editing) ? ' editing' : ''}`}
            value={value}
            options={options}
            onChange={val => {
                if (editing) onChange(val);
            }}
            onBlur={() => {rest?.onBlurkCell && rest.onBlurkCell()}}
            onClick={() => {
                if (!editing && onClickCell) onClickCell();
            }}
            
            {...rest}
        />
    );
};

