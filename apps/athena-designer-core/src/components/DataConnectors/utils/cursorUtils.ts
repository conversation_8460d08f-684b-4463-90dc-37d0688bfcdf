/**
 * 通用的光标位置插入工具函数
 * @param currentValue 当前输入框的值
 * @param insertText 要插入的文本
 * @param inputRef 输入框的 ref
 * @returns 插入后的新值
 */
export const insertTextAtCursor = (currentValue: string, insertText: string, inputRef: React.RefObject<any>): string => {
    const inputWrapper = inputRef.current;
    if (!inputWrapper) return currentValue + insertText;
    
    // 对于 Antd Input 组件，真正的 input 元素在 .input 属性中
    const input = inputWrapper.input || inputWrapper;
    
    const start = input.selectionStart;
    const end = input.selectionEnd;
    
    // 在光标位置插入文本
    const newValue = currentValue.substring(0, start) + insertText + currentValue.substring(end);
    
    // 设置新的光标位置
    setTimeout(() => {
        const newCursorPos = start + insertText.length;
        input.setSelectionRange(newCursorPos, newCursorPos);
        input.focus();
    }, 0);
    
    return newValue;
};

/**
 * 通用的文本区域光标位置插入工具函数
 * @param currentValue 当前文本区域的值
 * @param insertText 要插入的文本
 * @param textareaRef 文本区域的 ref
 * @returns 插入后的新值
 */
export const insertTextAtCursorTextarea = (currentValue: string, insertText: string, textareaRef: React.RefObject<any>): string => {
    const textareaWrapper = textareaRef.current;
    if (!textareaWrapper) return currentValue + insertText;
    
    // 对于 Antd TextArea 组件，真正的 textarea 元素在 .resizableTextArea.textArea 属性中
    const textarea = textareaWrapper.resizableTextArea?.textArea || textareaWrapper;
    
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    // 在光标位置插入文本
    const newValue = currentValue.substring(0, start) + insertText + currentValue.substring(end);
    
    // 设置新的光标位置
    setTimeout(() => {
        const newCursorPos = start + insertText.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
    }, 0);
    
    return newValue;
}; 