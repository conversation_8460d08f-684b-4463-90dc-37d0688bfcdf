import axios, { AxiosRequestConfig } from 'axios';
import { FormDataRequest, RequestOptions } from '../types/request';
import { VariableItem } from '../../DataVariables/types';
import { safeParse } from '../../DataVariables/utils';
import { buildVariablesMap, replaceVariables } from './variable-utils';

// 工具函数：数组转对象
function arrToObj(arr: Array<{ key: string; value: any }>, variablesMap: Record<string, any>) {
  return Array.isArray(arr)
    ? arr.reduce((acc, cur) => {
        if (cur.key) {
          acc[cur.key] = typeof cur.value === 'string'
            ? replaceVariables(cur.value, variablesMap)
            : cur.value;
        }
        return acc;
      }, {})
    : {};
}

// 执行 JS 脚本（让 context 可读写）
function runScript(script: string, context: Record<string, any> = {}) {
  if (!script) return;
  // 允许 script 直接操作 context（如 context.options）
  return new Function('context', `with(context){${script}}`)(context);
}


export function parseFormDataToRequestOptions(
  formData: any,
  variables?: VariableItem[]
): RequestOptions {
  const baseInfo = formData.baseInfo || {};
  const paramsInfo = formData.paramsInfo || {};
  const variablesMap = buildVariablesMap(variables);

  const params = arrToObj(paramsInfo.params || [], variablesMap);
  const headers = arrToObj(paramsInfo.headers || [], variablesMap);

  // 处理 body
  let data;
  if (paramsInfo.body?.content) {
    paramsInfo.body.content = replaceVariables(paramsInfo.body.content, variablesMap);
  }
  data = safeParse(paramsInfo.body?.content);

  // 处理脚本
  const preScript = replaceVariables(paramsInfo.preEvent?.preProcess || '', variablesMap);
  const postScript = replaceVariables(paramsInfo.preEvent?.postProcess || '', variablesMap);

  return {
    url: replaceVariables(baseInfo.path, variablesMap),
    method: baseInfo.method || 'get',
    params,
    data,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    preScript,
    postScript,
    timeout: 10000,
    retry: 3,
    retryDelay: 1000,
  };
}

export async function requestFactory(options: RequestOptions) {
  // 构造 context，让 preScript 能操作 options
  const preContext = { request: options };

  let finalOptions = preContext.request;
  // 前置 JS
  if (options.preScript) {
    finalOptions = runScript(options.preScript, preContext);
  }

  try {
    const response = await axios(finalOptions as AxiosRequestConfig);

    let data = response.data;

    // 后置 JS
    if (options.postScript) {
      data = runScript(options.postScript, { response: data });
    }

    return data;
  } catch (error) {
    // 这里可以加日志或自定义错误处理
    throw error;
  }
}