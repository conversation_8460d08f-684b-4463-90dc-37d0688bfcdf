import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { t } from 'i18next';
import { Dropdown, Input, Menu } from 'antd';
import './index.less';
import { DownOutlined } from '@ant-design/icons';

export interface EditTagModalProps {
  value?: string;
  options: any[];
  onChange?: (values: any) => void;
}

const SelectIntegrationInput: React.FC<EditTagModalProps> = (props: EditTagModalProps) => {
  const { value, options, onChange } = props;

  const menuItems = useMemo(() => {
    return options?.map((option) => ({
      key: option.value,
      label: option.value,
      onClick: () => handleSelect(option.value),
    }));
  }, [options]);

  const handleSelect = useCallback((value: string) => {
    onChange(value);
  }, []);

  return (
    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      <Dropdown
        overlayClassName="tag-dropdown-overlay"
        className={menuItems?.length > 0 ? '' : 'no-select-item-hide'}
        trigger={['click']}
        dropdownRender={() => <Menu items={menuItems} />}
      >
        <Input
          value={value}
          onChange={onChange}
          placeholder={t('dj-标签名称')}
          suffix={<DownOutlined />}
          style={{ width: '100%' }}
        />
      </Dropdown>
    </div>
  );
};

export default SelectIntegrationInput;
