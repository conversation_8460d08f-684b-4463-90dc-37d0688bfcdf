import { create } from 'zustand';
import { RuleManageOriginData } from '../../config/ruleManage.type';

interface RuleManageStore {
  isLoading: boolean; // 加载状态
  setIsLoading: (value: boolean) => void;
  ruleManageOriginData: RuleManageOriginData; // 规则组件原始数据
  setRuleManageOriginData: (value: RuleManageOriginData) => void;
  contextDataSourceName: string; // 上下文数据源名称
  setContextDataSourceName: (value: string) => void;
}

export const useRuleManageStore = create<RuleManageStore>((set) => ({
  isLoading: false,
  setIsLoading: (value) => set(() => ({ isLoading: value })),
  ruleManageOriginData: {
    isOffline: false, // 默认为在线模式
  },
  setRuleManageOriginData: (value) => set(() => ({ ruleManageOriginData: value })),
  contextDataSourceName: '',
  setContextDataSourceName: (value) => set(() => ({ contextDataSourceName: value })),
}));
