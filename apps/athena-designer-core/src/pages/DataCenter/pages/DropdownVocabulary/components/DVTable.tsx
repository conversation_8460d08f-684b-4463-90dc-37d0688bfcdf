import React, { Fragment, useContext, useMemo, useState } from 'react';
import { Table, Popconfirm, App, Checkbox, Button, Flex, Pagination } from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  DownCircleOutlined,
  UpCircleFilled,
} from '@ant-design/icons';
import AppButton from '@/components/AppButton';
import DVExpandTableRow from './DVExpandTableRow';
import { DVModuleContext } from './DVModuleStateWrapper';
import { useTranslation } from 'react-i18next';
import useGlobalStore from '@/store';
import useAxios from '../hooks/useAxios';
import serviceConfig from '../tools';
import { useDataStandardStore } from '@/pages/DataCenter/store';
import useDebounce from '../hooks/useDebounce';
import { DV_MODULE_ACTION_ENUM, EAuditStatus } from '../hooks/useDVModuleStateHook';
import { EditActionType } from './EditModal';
import useLoading from '../hooks/useLoading';
import AppDialog from '@/components/AppDialog';
import { AuthWrapper, Icon } from '@/components';
import './dvTable.less';

import type { TableColumnsType, PaginationProps } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import { ResourceType } from '@/common/utils/auth-util';

export enum DV_STANDARD {
  YES = 'Y',
  NO = 'N',
}

// type alias
type TableRes = DV.TAjaxRes<DV.AjaxPaginationData1<DV.RowData>>;

const DVTable = () => {
  const { message, modal } = App.useApp();
  const moduleContext = useContext(DVModuleContext);
  const { t, i18n } = useTranslation();
  const { userInfo } = (useGlobalStore.getState() as any) || {};
  const {
    state: { queryParams, selectedRows },
    setDVState,
  } = moduleContext;
  const { isDataStandardPermission } = useDataStandardStore();
  const [tipInfo, setTipInfo] = useState<DV.TipInfoType>({
    isShow: false,
    id: [],
    referenced: '',
    nonTenant: '',
  });

  const transformTableData = (input: TableRes): TableRes => {
    if (input?.data?.records?.length > 0) {
      input.data.records = input.data.records.map((value) => {
        const isStandAndAudited =
          value.standard === 'Y' && value.auditStatus === EAuditStatus.AUDIT_PASS;
        return {
          ...value,
          authDisabled: isStandAndAudited ? true : !isDataStandardPermission && isStandAndAudited,
          tenantIdDisabled: userInfo.tenantId !== value.tenantId,
        };
      });
    }

    return input;
  };

  const { data, fetch, loading } = useAxios<TableRes, TableRes>(
    serviceConfig.dictionaryPageQuery,
    transformTableData
  );
  const { fetch: fetchEnum } = useAxios<DV.TAjaxRes>(serviceConfig.disableCheckEnum);
  const { fetch: fetchDelete } = useAxios<DV.AjaxCommonResponse>(serviceConfig.deleteEnum);

  //#region 转为标准
  const { fetch: fetchChangeToStand } = useAxios<DV.AjaxCommonResponse>(
    serviceConfig.changeToStandard
  );

  const { loading: toStandLoading, fn: toStandFn } = useLoading(async (keys: string[]) => {
    const result = await fetchChangeToStand(keys);
    if (result.code === 0) {
      message.success(t('dj-操作成功'));
      doReQuery();
    }
  }, []);

  const doChangeToStand = () => {
    const keys = selectedRows.filter((e) => e.standard === 'N').map((e) => e.enumKey);
    toStandFn(keys);
  };
  //#endregion

  //#region 审核通过 / 驳回
  const { fetch: fetchAuditPass } = useAxios<DV.AjaxCommonResponse>(serviceConfig.auditPass);
  const { loading: passLoading, fn: audioPassFn } = useLoading(async (keys: string[]) => {
    const result = await fetchAuditPass(keys);
    if (result.code === 0) {
      message.success(t('dj-操作成功'));
      doReQuery();
    }
  }, []);

  // 驳回
  const { fetch: fetchAuditReject } = useAxios<DV.AjaxCommonResponse>(serviceConfig.auditReject);
  const { loading: rejectLoading, fn: audioRejectFn } = useLoading(async (keys: string[]) => {
    const result = await fetchAuditReject(keys);
    if (result.code === 0) {
      message.success(t('dj-操作成功'));
      doReQuery();
    }
  }, []);

  const doAudioConfirm = (type: 'PASS' | 'REJECT') => {
    const isPass = type === 'PASS';
    modal.confirm({
      title: t('dj-确认n吗', { n: t(isPass ? 'dj-审核通过' : 'dj-审核驳回') }),
      icon: null,
      wrapClassName: 'modal-confirm',
      okButtonProps: {
        style: { fontSize: 13, padding: '0 20px', height: 28 },
      },
      cancelButtonProps: {
        style: { fontSize: 13, padding: '0 20px', height: 28 },
      },
      content: isPass ? (
        <div style={{ fontSize: '14px', color: '#999', margin: '0 0 16px 0' }}>
          {t('dj-n后数据不可编辑', { n: t('dj-审核通过') })}
        </div>
      ) : null,
      okText: t('dj-确定'),
      cancelText: t('dj-取消'),
      onOk: () => {
        const keys = selectedRows
          .filter((e) => e.standard === 'Y' && e.auditStatus === EAuditStatus.TO_AUDIT)
          .map((e) => e.enumKey);
        if (type === 'PASS') audioPassFn(keys);
        else audioRejectFn(keys);
      },
    });
  };

  //#region

  useDebounce({
    callback: () => {
      fetch({
        ...queryParams,
      });
    },
    dependencies: [queryParams],
  });

  const doPagination = (page: number, pageSize: number) => {
    setDVState({
      type: DV_MODULE_ACTION_ENUM.PAGE_CHANGE,
      data: {
        pageSize: pageSize,
        pageIndex: page,
      },
    });
  };

  const doReQuery = () => {
    setDVState({
      type: DV_MODULE_ACTION_ENUM.SET_SELECTROWS,
      data: [],
    });
    setDVState({
      type: DV_MODULE_ACTION_ENUM.TABLE_REQUERY,
    });
  };

  const doCheck = async (record: DV.RowData[]): Promise<DV.CheckReqRes> => {
    const data = await fetchEnum(
      record.map((e) => ({
        id: e.id,
        key: e.key,
        tenantId: e.tenantId,
      }))
    );
    const { referenced, nonTenant } = data?.data ?? {};
    return {
      referenced,
      nonTenant,
    };
  };

  const doOperate = async (record: DV.RowData, opType: EditActionType) => {
    const isStandardAndAudited =
      record.standard === 'Y' && record.auditStatus === EAuditStatus.AUDIT_PASS;
    if (opType === EditActionType.COPY) {
      setDVState({
        type: DV_MODULE_ACTION_ENUM.OP_INFO,
        data: {
          isShow: true,
          info: record,
          disableCode: opType === EditActionType.COPY,
          disableCheckBox: false,
          opType: opType,
        } as DV.EditModalProps,
      });
    } else {
      if (isStandardAndAudited) {
        // 标准+通过的数据需要进行二次确认
        if (!(await doConfirm())) {
          return;
        }
      }
      setDVState({
        type: DV_MODULE_ACTION_ENUM.OP_INFO,
        data: {
          isShow: true,
          info: record,
          disableCode: opType === EditActionType.EDIT,
          disableCheckBox: false,
          opType: opType,
        } as DV.EditModalProps,
      });
    }
  };

  const doTipOk = () => {
    setTipInfo({
      referenced: '',
      nonTenant: '',
      isShow: false,
      id: [],
    });
  };

  const doConfirm = async (title?: string) => {
    return new Promise((res) => {
      modal.confirm({
        title: t('dj-提示'),
        icon: null,
        wrapClassName: 'modal-confirm',
        okButtonProps: {
          style: { fontSize: 13, padding: '0 20px', height: 28 },
        },
        cancelButtonProps: {
          style: { fontSize: 13, padding: '0 20px', height: 28 },
        },
        content: title || t('dj-标准数据会被引用，请谨慎操作'),
        okText: t('dj-确定'),
        cancelText: t('dj-取消'),
        onCancel: () => res(false),
        onOk: () => res(true),
      });
    });
  };

  const { loading: deleteLoading, fn: doDelete } = useLoading(async (records: DV.RowData[]) => {
    // 是否包含标准+审核通过的数据
    // const containStandardAndAudited = records.some(
    //   (record) => record.standard === 'Y' && record.auditStatus === EAuditStatus.AUDIT_PASS
    // );
    // const confirmContent = containStandardAndAudited ? undefined : t('dj-是否删除数据？');
    // if (await doConfirm(confirmContent)) {
    //   deleteData(records);
    // }
    deleteData(records);
  }, []);

  const deleteData = async (records: DV.RowData[]) => {
    const deleteData = await fetchDelete({
      keys: records.map((record) => record.enumKey),
    });
    if (deleteData?.code === 0) {
      message.success(t('dj-删除成功'));
      setDVState({
        type: DV_MODULE_ACTION_ENUM.SET_SELECTROWS,
        data: [],
      });
      doReQuery();
    } else {
      message.error(deleteData.msg as unknown as string);
    }
  };

  const columns: TableColumnsType<DV.RowData> = [
    {
      title: '',
      dataIndex: 'key',
      width: 0,
      render: () => null,
    },
    Table.EXPAND_COLUMN,
    {
      title: t('dj-下拉辞汇编码'),
      dataIndex: 'key',
      width: '240px',
      key: 'target_data',
    },
    {
      title: t('dj-下拉辞汇说明'),
      key: 'description',
      width: '320px',
      render: (_, record) => {
        return <p>{record?.lang?.description?.[i18n.language]}</p>;
      },
    },
    {
      title: t('dj-标准'),
      key: 'standard',
      width: '80px',
      render: (_, record) => {
        if (record?.standard === DV_STANDARD.YES) {
          return <CheckOutlined style={{ color: 'green' }} />;
        } else if (record?.standard === DV_STANDARD.NO) {
          return <CloseOutlined style={{ color: 'red' }} />;
        } else {
          return null;
        }
      },
    },
    {
      title: t('dj-审核'),
      key: 'auditStatus',
      width: '80px',
      render: (_, record) => {
        if (record?.auditStatus === EAuditStatus.AUDIT_PASS) {
          return <CheckOutlined style={{ color: 'green' }} />;
        } else if (record?.auditStatus === EAuditStatus.TO_AUDIT) {
          return <CloseOutlined style={{ color: 'red' }} />;
        } else if (record?.auditStatus === EAuditStatus.NO_AUDIT) {
          return '-';
        } else {
          return null;
        }
      },
    },
    {
      title: t('dj-最近修改人'),
      key: 'editBy',
      width: '160px',
      dataIndex: 'editBy',
    },
    {
      title: t('dj-最近修改时间'),
      key: 'editTime',
      width: '180px',
      dataIndex: 'editTime',
    },
    {
      title: t('dj-操作'),
      key: 'operate',
      fixed: 'right',
      width: '160px',
      render: (_, record) => {
        return (
          <Fragment>
            <AppButton
              type="link"
              size="small"
              disabled={
                ['1', '4'].includes(userInfo.teamId) ? false : userInfo.tenantId !== record.tenantId
              }
              className="clear-padding"
              onClick={() => doOperate(record, EditActionType.EDIT)}
            >
              {t('dj-编辑')}
            </AppButton>
            <Popconfirm
              description=""
              title={t('dj-确认删除？')}
              okText={t('dj-确定')}
              cancelText={t('dj-取消')}
              onConfirm={() => doDelete([record])}
            >
              <AppButton
                type="link"
                size="small"
                className="clear-padding"
                loading={deleteLoading && tipInfo.id.includes(record.id)}
                disabled={record.tenantIdDisabled || record.authDisabled}
              >
                {t('dj-删除')}
              </AppButton>
            </Popconfirm>
            <AppButton
              type="link"
              size="small"
              className="clear-padding"
              onClick={() => doOperate(record, EditActionType.COPY)}
            >
              {t('dj-复制')}
            </AppButton>
          </Fragment>
        );
      },
    },
  ];

  const rowSelection = useMemo((): TableRowSelection<DV.RowData> => {
    return {
      preserveSelectedRowKeys: true,
      fixed: 'left',
      type: 'checkbox',
      selectedRowKeys: selectedRows.map((e) => e.id),
      columnWidth: 50,
      onChange: (_, rows: DV.RowData[]) => {
        setDVState({
          type: DV_MODULE_ACTION_ENUM.SET_SELECTROWS,
          data: rows,
        });
      },
    };
  }, [selectedRows]);

  return (
    <Fragment>
      <div className="vocabulary-wrapper">
        <Table
          className="vocabulary-table"
          rowSelection={rowSelection}
          loading={loading}
          columns={columns}
          rowKey="id"
          expandable={{
            expandedRowRender: (record) => <DVExpandTableRow rowData={record} />,
            expandIcon: ({ expanded, onExpand, record }) =>
              expanded ? (
                <UpCircleFilled style={{ color: '#6b70c9' }} onClick={(e) => onExpand(record, e)} />
              ) : (
                <DownCircleOutlined onClick={(e) => onExpand(record, e)} />
              ),
          }}
          rowClassName={(record, index) => (index % 2 === 0 ? 'table-row-light' : 'table-row-dark')}
          dataSource={data?.data?.records ?? []}
          pagination={false}
          scroll={{ x: 1000, y: 'calc(100% - 40px)' }}
        />
        <div className="page-row">
          {data?.data?.records?.length > 0 ? (
            <div className="currentSelectedWrapper">
              <span>{t('dj-共n项', { n: data?.data?.total })},</span>
              <span>{t('dj-已选n项', { total: selectedRows.length })}</span>
              <AppButton
                type="link"
                onClick={() =>
                  setDVState({
                    type: DV_MODULE_ACTION_ENUM.SET_SELECTROWS,
                    data: [],
                  })
                }
              >
                {t('dj-全部取消勾选')}
              </AppButton>
            </div>
          ) : (
            <div></div>
          )}
          <Pagination
            className="pagination-toolbar"
            current={queryParams.pageIndex}
            pageSize={queryParams.pageSize}
            showSizeChanger
            total={data?.data?.total}
            onChange={doPagination}
          />
        </div>

        <AppDialog
          open={tipInfo.isShow}
          cancelText={null}
          title={null}
          okText={t('dj-我知道了')}
          onOk={doTipOk}
        >
          <div className="tipContnet">
            {tipInfo.referenced?.length > 0 && (
              <div className="tipContentWarning">
                <Icon className="iconfont" type="iconwarning" />
                <span>
                  {`${tipInfo.referenced} ${t('dj-下拉辞汇已被关联使用，不支持删除操作！')}`}
                </span>
              </div>
            )}
            {tipInfo.nonTenant?.length > 0 && (
              <div className="tipContentWarning">
                <Icon className="iconfont" type="iconwarning" />
                <span>
                  {`${tipInfo.nonTenant} ${t('dj-下拉辞汇非本租户创建，不支持删除操作！')}`}
                </span>
              </div>
            )}
          </div>
        </AppDialog>
        <Flex wrap="wrap" gap="middle" justify="center" className="footer">
          <AuthWrapper
            authParams={{
              action: 'update',
            }}
          >
            <AppButton
              size="middle"
              className="opButton outline"
              loading={toStandLoading}
              disabled={!selectedRows.length || !selectedRows.some((e) => e.standard === 'N')}
              onClick={doChangeToStand}
            >
              {t('dj-转为标准')}
            </AppButton>
          </AuthWrapper>
          <AppButton
            className="opButton outline"
            disabled={
              !selectedRows.length || selectedRows.some((e) => e.tenantIdDisabled || e.authDisabled)
            }
            loading={deleteLoading}
            onClick={() => doDelete(selectedRows)}
          >
            {t('dj-删除')}
          </AppButton>
          {queryParams.auditStatus === EAuditStatus.TO_AUDIT ? (
            <>
              <AuthWrapper
                authParams={{
                  action: 'update',
                }}
              >
                <AppButton
                  className="opButton outline"
                  disabled={
                    !selectedRows.length ||
                    !selectedRows.some(
                      (e) => e.auditStatus === EAuditStatus.TO_AUDIT && e.standard === 'Y'
                    )
                  }
                  onClick={() => doAudioConfirm('PASS')}
                  loading={passLoading}
                >
                  {t('dj-审核通过')}
                </AppButton>
              </AuthWrapper>
              <AuthWrapper
                authParams={{
                  action: 'update',
                }}
              >
                <AppButton
                  className="opButton outline"
                  disabled={
                    !selectedRows.length ||
                    !selectedRows.some(
                      (e) => e.auditStatus === EAuditStatus.TO_AUDIT && e.standard === 'Y'
                    )
                  }
                  onClick={() => doAudioConfirm('REJECT')}
                  loading={rejectLoading}
                >
                  {t('dj-审核驳回')}
                </AppButton>
              </AuthWrapper>
            </>
          ) : null}
        </Flex>
      </div>
    </Fragment>
  );
};

export default DVTable;
