const serviceConfig: DV.ServiceConfig = {
  authQuery: {
    url: '/athena-designer/dataStandard/auth/query',
  },
  dictionaryPageQuery: {
    url: '/athena-designer/dictionary/queryDictionaryPageable',
  },
  disableCheckEnum: {
    url: '/athena-designer/dictionary/disable/check',
  },
  changeToStandard: {
    url: '/athena-designer/dictionary/changeToStandard',
  },
  auditPass: {
    url: '/athena-designer/dictionary/audit?type=pass',
  },
  auditReject: {
    url: '/athena-designer/dictionary/audit?type=reject',
  },
  translate: {
    url: '/athena-designer/translate/translate',
  },
  addEnum: {
    url: '/athena-designer/dictionary/V2/add',
  },
  saveEnum: {
    url: '/athena-designer/dictionary/V2/modify',
  },
  deleteEnum: {
    url: '/athena-designer/dictionary/V2/batchDelete',
  },
  downloadTemplate: {
    url: '/athena-designer/dataStandard/templateDownload',
    responseType: 'blob',
  },
};

export default serviceConfig;
