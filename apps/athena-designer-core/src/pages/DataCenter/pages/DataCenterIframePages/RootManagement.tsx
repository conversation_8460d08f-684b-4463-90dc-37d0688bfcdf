import React, { useMemo } from 'react';
import './common.less';
import { getdDataCenterPageUrl } from '../../common/config/common-config';
import useGlobalStore from '@/store';

const RootManagement: React.FC = () => {
  const userInfo = (useGlobalStore.getState() as any).userInfo || {};
  const iframeUrl = useMemo(() => {
    const { iamToken, tenantSid } = userInfo;
    return getdDataCenterPageUrl(iamToken, tenantSid, 'standard/rootManagement');
  }, [userInfo]);

  return <iframe className="iframe-container" src={iframeUrl}></iframe>;
};

export default RootManagement;
