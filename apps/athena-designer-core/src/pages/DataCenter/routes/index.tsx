import React from 'react';
import DeployerLayout from '..';
import { Navigate } from 'react-router-dom';
import useGlobalStore from '@/store';

const DataCenterRedirect = () => {
  const { isPrivatization } = useGlobalStore.getState() as any;
  return (
    <Navigate
      to={isPrivatization ? '/data-center/business-type' : '/data-center/root-management'}
    />
  );
};

export default [
  {
    path: 'data-center',
    component: <DeployerLayout />,
    children: [
      {
        path: '',
        component: <DataCenterRedirect />,
      },
      {
        path: 'business-type', // 业务类型
        component: import('@/pages/DataCenter/pages/BusinessType'),
      },
      {
        path: 'dropdown-vocabulary', // 下拉词汇维护
        component: import('@/pages/DataCenter/pages/DropdownVocabulary'),
      },
      {
        path: 'vocabulary-dictionary', // 辞汇字典维护
        component: import('@/pages/DataCenter/pages/VocabularyDictionary'),
      },
      {
        path: 'data-publish', // 数据发布
        component: import('@/pages/DataCenter/pages/DataPublish'),
      },
      {
        // 词根管理
        path: 'root-management',
        component: import('@/pages/DataCenter/pages/DataCenterIframePages/RootManagement'),
      },
      {
        // 字典管理
        path: 'dict-management',
        component: import('@/pages/DataCenter/pages/DataCenterIframePages/DictManagement'),
      },
      {
        // 数据元管理（命名和数据中台保持一致）
        path: 'data-ele-manage',
        component: import('@/pages/DataCenter/pages/DataCenterIframePages/DataEleManage'),
      },
      {
        // 表示类管理（命名和数据中台保持一致）
        path: 'expression-type-list',
        component: import('@/pages/DataCenter/pages/DataCenterIframePages/ExpressionTypeList'),
      },
    ],
  },
];
