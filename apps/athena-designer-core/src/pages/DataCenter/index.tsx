import React, { useEffect, useMemo, useState } from 'react';
import { SubLayout } from '@/components';
import { useTranslation } from 'react-i18next';
import {
  lcdpRoutes,
  dataCenterRoutes,
  RoutesType,
} from './common/config/data-center-routes-config';
import './assets/css/index.less';
import { useDataStandardStore } from './store';
import { ResourceType, getAuth } from '@/common/utils/auth-util';
import useGlobalStore from '@/store';
import {
  individualLcdpRoutes,
  individualDataCenterRoutes,
} from './common/config/individual-data-center-routes-config';

const App = () => {
  const { t } = useTranslation();
  const [roles, setRoles] = React.useState([]);
  const [router, setRouter] = useState<RoutesType[]>([]);
  const { setIsDataStandardPermission } = useDataStandardStore((state) => ({
    setIsDataStandardPermission: state.setIsDataStandardPermission,
  }));
  const { isPrivatization } = useGlobalStore.getState() as any;

  useEffect(() => {
    const allowInfo = getAuth({
      action: 'update',
    });
    setIsDataStandardPermission(allowInfo);
    const routes = isPrivatization ? lcdpRoutes : dataCenterRoutes;

    if (!allowInfo) {
      setRouter(routes.filter((e) => e.id !== 'data-center/data-publish'));
    } else {
      setRouter(routes);
    }
  }, []);

  useEffect(() => {
    let roles = (useGlobalStore.getState() as any)?.auth?.roles || [];
    roles = roles?.filter((item) => item.status === 'PASSED');
    setRoles(roles);
    const unsubscribe = useGlobalStore.subscribe((state: any, previousState: any) => {
      if (state.roles !== previousState.roles) {
        const newRoles = (state.roles || [])?.filter((item) => item.status === 'PASSED');
        setRoles(newRoles);
      }
    });

    const { individualCase } = (useGlobalStore.getState() as any)?.individual ?? {};
    if (individualCase) {
      const routes = isPrivatization ? individualLcdpRoutes : individualDataCenterRoutes;
      setRouter(routes);
    }

    return unsubscribe;
  }, []);

  return (
    <section className="data-center-layout-wrapper">
      <SubLayout
        title={t('dj-数据中心')}
        roles={roles}
        resourcePrefix={ResourceType.DATA_STANDARD}
        menuItem={router}
      />
    </section>
  );
};

export default App;
