enum Environment {
  DEV = 'dev',
  PAAS = 'paas',
  TEST = 'test',
  PROD = 'prod',
}

const lcdpDomainMap = {
  'athena-dev-platform-paas.digiwincloud.com.cn': Environment.PAAS,
  'athena-dev-platform-test.digiwincloud.com.cn': Environment.TEST,
  'athena-dev-platform.digiwincloud.com.cn': Environment.PROD,
};

// 数据中台的url
const dataCenterUrl = {
  // [Environment.DEV]: 'http://************:9003',
  [Environment.DEV]: 'http://************:8004',
  // [Environment.PAAS]: 'http://************:8004',
  [Environment.PAAS]: 'https://hw-test-dmp-dmf.digiwincloud.com.cn', // 经过沟通，PAAS区用TEST环境的url
  [Environment.TEST]: 'https://hw-test-dmp-dmf.digiwincloud.com.cn',
  [Environment.PROD]: 'https://dmp-dmf-hw.digiwincloud.com.cn',
};

export const getLcdpEnvironment = (): Environment => {
  if (process.env.NODE_ENV === 'development') return Environment.DEV;
  const currentHost = window.location.hostname;
  return lcdpDomainMap[currentHost] || Environment.PROD; // 默认返回生产环境
};

export const getdDataCenterUrl = (): string => {
  return `${dataCenterUrl[getLcdpEnvironment()]}/sso-login`;
};

export const getdDataCenterPageUrl = (iamToken, tenantSid, redirectPath): string => {
  const url = new URL(getdDataCenterUrl());
  const searchParams = new URLSearchParams();
  searchParams.append('userToken', iamToken);
  searchParams.append('redirectPath', redirectPath);
  searchParams.append('accessSourceApp', 'lcdp');
  searchParams.append('tenantId', tenantSid);
  url.search = searchParams.toString();
  return url.toString();
};
