export interface RoutesType {
  id: string;
  path: string;
  type: string;
  programId: string;
  hideBranch?: string[];
}

// 使用开发平台的数据标准的路由配置
export const lcdpRoutes: RoutesType[] = [
  {
    id: 'data-center/business-type',
    path: 'business-type',
    type: 'program',
    programId: 'data-center/business-type',
    hideBranch: ['fusion'],
  },
  {
    id: 'data-center/dropdown-vocabulary',
    path: 'dropdown-vocabulary',
    type: 'program',
    programId: 'data-center/dropdown-vocabulary',
    hideBranch: ['fusion'],
  },
  {
    id: 'data-center/vocabulary-dictionary',
    path: 'vocabulary-dictionary',
    type: 'program',
    programId: 'data-center/vocabulary-dictionary',
    hideBranch: ['fusion'],
  },
  {
    id: 'data-center/data-publish',
    path: 'data-publish',
    type: 'program',
    programId: 'data-center/data-publish',
    hideBranch: ['fusion'],
  },
];

// 使用数据中台的数据标准的路由配置
export const dataCenterRoutes: RoutesType[] = [
  {
    id: 'data-center/root-management', // 词根管理
    path: 'root-management',
    type: 'program',
    programId: 'data-center/root-management',
    hideBranch: ['fusion'],
  },
  {
    id: 'data-center/dict-management', // 字典管理
    path: 'dict-management',
    type: 'program',
    programId: 'data-center/dict-management',
    hideBranch: ['fusion'],
  },
  {
    id: 'data-center/data-ele-manage', // 数据元管理
    path: 'data-ele-manage',
    type: 'program',
    programId: 'data-center/data-ele-manage',
    hideBranch: ['fusion'],
  },
  {
    id: 'data-center/expression-type-list', // 表示类管理
    path: 'expression-type-list',
    type: 'program',
    programId: 'data-center/expression-type-list',
    hideBranch: ['fusion'],
  },
  {
    id: 'data-center/data-publish',
    path: 'data-publish',
    type: 'program',
    programId: 'data-center/data-publish',
    hideBranch: ['fusion'],
  },
];
