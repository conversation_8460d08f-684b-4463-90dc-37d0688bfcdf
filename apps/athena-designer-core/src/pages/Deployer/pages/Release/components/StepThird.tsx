import React, { useCallback, useEffect, useState } from 'react';
import useStore from '../store';
import { useTranslation } from 'react-i18next';
import AlertTip from './AlertTip';
import { App, Button, Spin, Tabs, TabsProps } from 'antd';
import TabItem from './TabItem';
import { useAllProgressQuery, useDeploy } from '../api/query';
import useGlobalStore from '@/store';
import { Icon } from '@/components';

const buttonText = {
  fail: 'dj-返回',
  success: 'dj-完成',
};

const StepThird: React.FC<any> = () => {
  const { message } = App.useApp();
  const { modal } = App.useApp();
  const { t } = useTranslation();
  const versionUpdateData = useStore((state) => state.versionUpdateData);
  const selectedApps = useStore((state) => state.selectedApps);
  const reset = useStore((state) => state.reset);
  const { config, userInfo, currentLanguage } = (useGlobalStore.getState() as any) || {};

  const [tabKey, setTabKey] = useState(selectedApps[0].code);

  /* querys */
  const { data, status } = useDeploy(versionUpdateData);
  // 获取所有解决方案总进度条
  const allApplication = (versionUpdateData?.applicationDataList || [])
    .map((i) => i.application)
    .join(',');

  // 查询整个进度条
  const { data: progressData } = useAllProgressQuery({ deployNo: data?.data, allApplication });

  const tabs: TabsProps['items'] = (selectedApps || []).map((item, index) => ({
    key: item.code,
    label: item.name,
    children: <TabItem deployNo={data?.data} currentTabKey={tabKey} application={item.code} />,
  }));

  useEffect(() => {
    if (!data) return;
    if (data.code === -1) {
      message.error(data.msg);
    }
  }, [data]);

  useEffect(() => {
    if (progressData) {
      if (progressData?.data === 'fail') {
        message.error(t('dj-版更失败'));
      }
      if (progressData?.data === 'success') {
        showReleaseSuccessModal();
        window.microApp.dispatch({ type: 'publish', data: { published: true } });
      }
    }
  }, [progressData]);

  /**
   * 体验环境下 + 体验用户，编译成功之后，跳转至云形态
   */
  const showReleaseSuccessModal = useCallback(() => {
    if (config.envAlias === 'EDU' && userInfo.experience) {
      modal.confirm({
        closable: true,
        title: undefined,
        wrapClassName: 'release-wrap',
        content: (
          <div>
            <Icon className="iconfont release-ok" type="icongreen-hook" />
            <div>{t('dj-发版成功，立即查看运行效果')}</div>
          </div>
        ),
        cancelText: t('dj-取消'),
        okText: t('dj-立即查看'),
        onOk: async () => {
          const token = userInfo.iamToken;
          const language = currentLanguage || 'zh_CN';
          window.open(
            `${config.athenaUrl}/sso-login?routerLink=base-data-entry&userToken=${token}&dwLang=${language}`,
            '_blank'
          );
        },
      });
    } else {
      message.success(t('dj-已完成版更'));
    }
  }, [config, userInfo, currentLanguage]);

  if (status === 'pending') {
    return <Spin />;
  }

  return (
    <>
      <AlertTip application={tabKey} />
      <Tabs items={tabs} onChange={(key) => setTabKey(key)} />
      <div className="button-bar-holder">
        <div className="button-bar">
          {Object.keys(buttonText).includes(progressData?.data) && (
            <Button className="option-button" type="primary" size="large" onClick={reset}>
              {t(buttonText[progressData?.data])}
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

export default React.memo(StepThird);
