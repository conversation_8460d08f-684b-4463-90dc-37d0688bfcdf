import useGlobalStore from '@/store';
import { App, FormInstance } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { IRemoteDeploy } from '../types';
import useStore from '../store';
import { useTranslation } from 'react-i18next';

/**
 * 初始化form
 * @param data
 */
export const useInitFormData = (data: any, form: FormInstance<any>) => {
  const chooseCompiledData = useStore((state) => state.chooseCompiledData);
  // 组装formData
  useEffect(() => {
    if (!data) return;
    const formData = {
      applicationDataList: (data?.data || []).map((item) => ({
        application: item.application,
        compileDataCode: item.compiledDataList[0].code,
        applicationInfoDto: item,
      })),
      tenantList: [],
      tenantUsers: [],
      env: null,
      syncEsp: false,
    };
    // 初始化form
    form.setFieldsValue(formData);
  }, [data]);

  useEffect(() => {
    if (chooseCompiledData?.compileDataCode) {
      // 如果是直接点解决方案的发版icon进入第二步，则设置compileDataCode默认值为第一步被选中的值
      form.setFieldValue(
        ['applicationDataList', 0, 'compileDataCode'],
        chooseCompiledData?.compileDataCode
      );
    }
  }, [chooseCompiledData]);
};

/**
 * 获取解决方案内编译并发版的解决方案
 * @returns
 */
export const useSpecialDeploy = (): IRemoteDeploy => {
  const [searchParams] = useSearchParams();
  const [data, setData] = useState(null);

  useEffect(() => {
    if (searchParams.get('deploy_app')) {
      // 要发版的解决方案数据
      // const appData = (useGlobalStore.getState() as any).sessionStorage['deployer@selected_app'];
      // 这里考虑到useGlobalStore.getState()与解决方案渲染是异步的，猜测会导致解决方案渲染时拿不到sessionStorage里的数据，因此这里暂时从window.microApp.getData()里获取
      const appData = window.microApp.getData().sessionStorage['deployer@selected_app'];
      if (appData) {
        setData(JSON.parse(appData));
      }
    }
  }, [searchParams.get('deploy_app')]);

  if (!!data) {
    return { deployAppCode: searchParams.get('deploy_app'), deployAppData: data?.[0] };
  }
  return { deployAppCode: null, deployAppData: null };
};

/**
 * Step1中点击下一步逻辑
 */
export const useClickNext = (refetch, appsCompileData) => {
  const { message } = App.useApp();
  const { t } = useTranslation();
  const [signal, setSignal] = useState(0);
  const selectedApps = useStore((state) => state.selectedApps);
  const setCompileApp = useStore((state) => state.setCompileApp);
  const setCompileVisible = useStore((state) => state.setCompileVisible);
  const setChooseCompiledData = useStore((state) => state.setChooseCompiledData);
  const setStep = useStore((state) => state.setStep);
  const setMaxStep = useStore((state) => state.setMaxStep);

  const handleNext = useCallback(async () => {
    await refetch();
    setSignal((signal) => signal + 1);
  }, [selectedApps]);

  useEffect(() => {
    if (signal === 0) return;
    // 只选中一个app且该app没编译过
    if (selectedApps?.length === 1 && !appsCompileData?.data?.[0]?.hasCompileData) {
      setCompileApp(selectedApps[0]);
      setCompileVisible(true);
      return;
    }
    // 选择多个app
    if (selectedApps?.length > 1) {
      const noPass = [];
      selectedApps.forEach((app) => {
        const currentItem = (appsCompileData.data || []).find(
          (item) => item.application === app.code
        );
        if (!currentItem?.hasCompileData) {
          noPass.push(app.name);
        }
      });
      if (noPass.length > 0) {
        message.error(
          t('dj-没有编译包，请选择带有编译包的解决方案！', { message: noPass.join(',') })
        );
        return;
      }
    }
    // 清空store里的chooseCompiledData
    setChooseCompiledData({});
    setStep(1);
    setMaxStep(1);
  }, [signal]);

  return { handleNext };
};

/**
 * 选择编译包弹窗 表单onFinish事件回调
 * @param chooseCompiledData
 * @returns
 */
export const useCompilePackageFormFinish = (
  chooseCompiledData
): { onFinish: (formData: any) => void } => {
  const compileApp = useStore((state) => state.compileApp);
  const setChooseCompileVisible = useStore((state) => state.setChooseCompileVisible);
  const setChooseCompiledData = useStore((state) => state.setChooseCompiledData);
  const resetSelectedApps = useStore((state) => state.resetSelectedApps);
  const checkedApp = useStore((state) => state.checkedApp);
  const setStep = useStore((state) => state.setStep);
  const setMaxStep = useStore((state) => state.setMaxStep);

  const onFinish = useCallback(
    (formData) => {
      const chooseCompiledData = useStore.getState().chooseCompiledData;

      // 设置发版的解决方案数据
      setChooseCompiledData({
        ...(chooseCompiledData || {}),
        compileDataCode: formData.compileDataCode,
      });
      // 设置当前解决方案为被选中的app
      resetSelectedApps();
      checkedApp({ ...(compileApp || {}) });
      setChooseCompileVisible(false);
      setStep(1);
      setMaxStep(1);
    },
    [chooseCompiledData]
  );

  return { onFinish };
};

/**
 * 解决方案点击发版Icon回调
 * @param refetch
 * @param data
 * @param itemData
 * @returns
 */
export const useClickDeploy = (refetch, data, itemData): { handleDeploy: () => void } => {
  const { message } = App.useApp();
  const { t } = useTranslation();
  const [clickSignal, setClickSignal] = useState(0);
  const setCompileApp = useStore((state) => state.setCompileApp);
  const setChooseCompileVisible = useStore((state) => state.setChooseCompileVisible);
  const setChooseCompiledData = useStore((state) => state.setChooseCompiledData);

  const handleDeploy = useCallback(async () => {
    await refetch();
    setClickSignal(clickSignal + 1);
  }, [clickSignal]);

  /**
   * 点击发版
   */
  useEffect(() => {
    if (clickSignal === 0 || !data) return;
    if (!data?.data?.length) {
      message.error(t('dj-没有编译包，请选择带有编译包的解决方案！', { message: itemData.name }));
      return;
    }
    // 设置被选中的解决方案
    setCompileApp(itemData);
    // 打开编译包弹窗
    setChooseCompileVisible(true);
    // 设置编译包弹窗所需要的数据
    setChooseCompiledData({
      name: itemData.name,
      compiledDataList: data.data[0].compiledDataList,
    });
  }, [clickSignal]);

  return { handleDeploy };
};

/**
 *
 * @param application
 * @param envList
 * @param data
 * @returns
 */
export const useTransformUrl = (
  application,
  envList,
  data
): { consoleUrl: string; handleGoUrl: () => void } => {
  const env = useStore((state) => state.env);
  const [clickSignal, setClickSignal] = useState(0);
  const { config, authToken, userInfo } = useGlobalStore() as any;

  const consoleUrl = `${config.consoleUrl}/sso-login?userToken=${
    authToken.iamToken
  }&routerLink=mang-authority&userId=${userInfo.userId}&appId=${application || ''}`;

  const handleGoUrl = useCallback(() => {
    setClickSignal((n) => n + 1);
  }, []);

  // 点击雅典娜前端
  useEffect(() => {
    if (!clickSignal) return;
    const origin = (envList || []).find((item) => env == item.env)?.platformAddress;
    let url = `${origin}/sso-login?userToken=${
      authToken.iamToken
    }&routerLink=todo/project&appCode=${application}&experience=${false}`;
    if (data?.data) url = url + `&guideScript=${data?.data}`;
    window.open(url, '_blank');
  }, [clickSignal]);

  return { consoleUrl, handleGoUrl };
};

/**
 * 初始化默认的选择环境、选择租户
 * @param lastestInfo
 * @param form
 */
export const useInitEnvTenant = (
  lastestInfo,
  tenantsData,
  mount,
  form: FormInstance<any>,
  setTenantId
) => {
  // 添加的租户
  const setEnv = useStore((state) => state.setEnv);

  useEffect(() => {
    /* 初始化默认的选择环境、选择租户 */
    if (lastestInfo?.data?.latestEnv && mount) {
      const { latestEnv } = lastestInfo.data;
      form.setFieldsValue({ env: latestEnv });
      setEnv(latestEnv);
    }

    if (lastestInfo?.data?.latestTenant && tenantsData?.length && mount) {
      const { latestTenant } = lastestInfo.data;
      // 如果上次信息中的租户在租户列表里，则默认选中
      if (tenantsData.find((i) => i.id === latestTenant)) {
        form.setFieldsValue({ tenantList: [latestTenant] });
        setTenantId(latestTenant);
      }
    }
  }, [lastestInfo, tenantsData, mount]);
};

/**
 * 选择租户时自动新增 tenantUsers
 * @param tenantId
 * @param deselectTenantId
 * @param tenantsData
 * @param form
 * @param deleteSource
 */
export const useIncreaseTenant = (
  tenantId,
  deselectTenantId,
  tenantsData,
  form: FormInstance<any>,
  deleteSource
) => {
  useEffect(() => {
    if (tenantsData?.length === 0) return;
    const { tenantUsers } = form.getFieldsValue();
    const addItem = (tenantsData || []).filter((item) => tenantId.includes(item.id));
    const newTenantUsers = (addItem || []).map((item) => ({
      option: 1,
      tenantId: item.id,
      tenantSid: item.sid,
    }));
    form.setFieldsValue({
      tenantUsers: [...(tenantUsers || []), ...(newTenantUsers || [])],
    });
  }, [tenantId, tenantsData]);
  useEffect(() => {
    if (deselectTenantId?.length === 0) return;
    // 根据deselectTenantId删除租户
    const currentTenants = form.getFieldValue(['tenantUsers']);
    const actualTenants = (currentTenants || []).filter(
      (i) => !deselectTenantId.includes(i.tenantId)
    );
    form.setFieldValue(['tenantUsers'], actualTenants);
    if (deleteSource === 'current') {
      const tenantList = form.getFieldValue(['tenantList']);
      form.setFieldValue(
        ['tenantList'],
        (tenantList || []).filter((i) => !deselectTenantId.includes(i))
      );
    }
  }, [deselectTenantId]);
};
