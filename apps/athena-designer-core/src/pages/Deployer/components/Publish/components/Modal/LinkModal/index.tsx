import { Modal, Space } from 'antd';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import './index.less';
import useStore from '../../../store';
import { IModalData } from '../../../types';
import NoData from '@/components/NoData';
import useGlobalStore from '@/store';

interface IProps {
  modalData: IModalData;
}

//demo数据 目前支持sso的四个
const dd = [
  {
    name: '鼎捷云控制台',
    code: 'athena_console',
    url: 'https://console-test.digiwincloud.com.cn/sso-login?userToken=@userToken',
    lang: { name: { zh_CN: '鼎捷云控制台', zh_TW: '鼎捷雲控制台', en_US: '' } },
  },
  {
    name: '鼎捷雅典娜',
    code: 'athena',
    url: 'https://athena-paas.digiwincloud.com.cn/sso-login?userToken=@userToken',
    lang: { name: { zh_CN: '鼎捷雅典娜', zh_TW: '鼎新metis', en_US: '' } },
  },
  {
    name: '服务编排日志',
    code: 'sc_console',
    url: 'https://sc-console-paas.digiwincloud.com.cn/#/ssoLogin?routerLink=workflows&token=@token&username=@username&lang=@lang&tenantid=@tenantid',
    lang: { name: { zh_CN: '服务编排日志', zh_TW: '服務編排日誌', en_US: '' } },
  },
  {
    code: 'dev_console',
    name: '开发者工作台',
    url: 'https://dev-console.digiwincloud.com.cn/sso-login?userToken=@userToken',
    lang: { name: { zh_CN: '开发者工作台', zh_TW: '開發者工作臺', en_US: '' } },
  },
];
// const needSso = ['athena_console', 'athena', 'sc_console', 'dev_console'];

const LinkModal: React.FC<IProps> = (props) => {
  const { modalData } = props;
  const setModalData = useStore((state) => state.setModalData);
  const iamToken = useGlobalStore((state: any) => state.authToken)?.iamToken;
  const userInfo = useGlobalStore((state: any) => state.userInfo);
  const { t } = useTranslation();

  /**
   * 取消
   */
  const handleCancel = useCallback(() => {
    setModalData(null);
  }, []);

  const handleCopyLink = useCallback(
    (item) => {
      const url = item.url
        .replace(/@token/g, iamToken)
        .replace(/@userToken/g, iamToken)
        .replace(/@username/g, userInfo.name)
        .replace(/@lang/g, t('dj-LANG'))
        .replace(/@tenantid/g, userInfo.tenantId);
      window.open(url, '_blank');
    },
    [iamToken, userInfo]
  );

  return (
    <Modal
      destroyOnClose={true}
      maskClosable={false}
      open={true}
      getContainer={false}
      title={`${modalData?.data?.serviceName}${t('dj-友情链接')}`}
      onCancel={handleCancel}
      footer={null}
      width={720}
      className="link-modal"
    >
      <div className="link-content">
        {modalData?.data?.friendlyLinkList?.length > 0 ? (
          <Space size={[0, 36]} wrap>
            {(modalData?.data?.friendlyLinkList || []).map((item, index) => (
              <span className="link-item" key={item.code} onClick={() => handleCopyLink(item)}>
                {item?.lang?.name?.[t('dj-LANG')] || item.name}
              </span>
            ))}
          </Space>
        ) : (
          <NoData description={t('dj-暂无友情链接')} />
        )}
      </div>
    </Modal>
  );
};

export default React.memo(LinkModal);
