import useGlobalStore from '@/store';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { CookieUtil } from '../utils/cookie-util';
import { message } from 'antd';
import i18n from 'i18next';
import { useIndividual } from '../hooks';
import { CustomEventName, HttpError } from '../types';

let tokenInvalidation = false;

// 请求拦截器
axios.interceptors.request.use(
  function (config) {
    const state = useGlobalStore.getState() as any;
    const { currentLanguage } = state;
    // 每个请求添加唯一标志uuid
    config.headers['X-Requested-With'] = uuidv4();
    // 设置语言别
    config.headers['locale'] = currentLanguage;
    // 设置token
    setToken(config);
    // 设置平台别
    config.headers['client-agent'] = 'webplatform';
    // 设置routerKey
    setRouterKey(config);
    // 设置appToken
    setAppToken(config);
    // 设置branch
    setBranch(config);
    // 设置租户级(租户级开发平台的branch固定设置为master)
    setTenant(config);
    // 设置个案应用
    setIndividualHeader(config);
    return config;
  },
  function (error) {
    // 执行请求错误的操作
    return Promise.reject(error);
  }
);

// 响应拦截器
axios.interceptors.response.use(
  function (response) {
    // 对响应数据做一些事情（code 2xx 范围内的状态代码都会导致此函数触发）
    if (
      !(response?.data instanceof Blob && response?.status === 200) &&
      response?.data.code !== 0
    ) {
      const { code, msg } = response?.data;
      const { url } = response?.config;
      const error = new HttpError(code, url, msg, response);
      handleError(error);
    }
    return response;
  },
  function (error) {
    // 手动取消的不需要自动message
    if (error.code !== 'ERR_CANCELED') {
      handleError(error);
    }
    // 响应错误做某些事情(任何超出 2xx 范围的状态代码都会导致此功能触发)
    return Promise.reject(error);
  }
);

// 设置appToken
function setAppToken(config): void {
  if (checkUrl('appToken', config.url)) {
    if (!config.headers['digi-middleware-auth-app']) {
      const { config: appConfig } = useGlobalStore.getState() as any;
      const { appToken } = appConfig ?? {};
      config.headers['digi-middleware-auth-app'] = appToken;
    }
  }
}

// 设置routerKey
function setRouterKey(config) {
  if (checkUrl('routerKey', config.url)) {
    const { userInfo } = useGlobalStore.getState() as any;
    const { tenantId } = userInfo;
    if (!!tenantId) {
      config.headers['routerKey'] = tenantId;
      if (CookieUtil.get('routerKey') !== tenantId) {
        CookieUtil.set('routerKey', tenantId);
      }
    }
  }
}

function setToken(config) {
  const { authToken } = useGlobalStore.getState() as any;
  // 需要传iamToken的判断
  if (authToken.iamToken && !config.headers['token']) {
    if (checkUrl('token', config.url)) {
      config.headers['token'] = authToken.iamToken;
    }
  }

  // 兼容api平台:用户token
  if (authToken.iamToken && !config['digi-middleware-auth-user']) {
    config.headers['digi-middleware-auth-user'] = authToken.iamToken;
  }

  // 开发平台token
  if (authToken.token && !config['Authorization']) {
    if (!config.url.includes('/iam/')) {
      config.headers['Authorization'] = authToken.token;
    }
  }
}

// 设置分支
function setBranch(config): void {
  const { userInfo } = useGlobalStore.getState() as any;
  const { branch } = userInfo;
  if (!!branch && checkUrl('branch', config.url)) {
    config.headers['branch'] = branch;
  }
}

// 设置租户级(租户级开发平台的branch固定设置为master)
function setTenant(config): void {
  const { userInfo } = useGlobalStore.getState() as any;
  const { isTenantActive } = userInfo;
  if (!!isTenantActive && checkUrl('branch', config.url)) {
    config.headers['branch'] = 'master';
  }
}

// 设置个案应用请求头
function setIndividualHeader(config): void {
  const { adesignerUrl, publishUrl } = (useGlobalStore.getState() as any).config || {};
  // 前端控制只有adp和aadc的域名加请求头
  if (![adesignerUrl, publishUrl].some((urlKey) => config.url.includes(urlKey))) {
    return;
  }
  // 接口白名单
  if (
    ['aadc'].some((urlKey) => config.url.includes(urlKey)) &&
    ![`${publishUrl}/athenadeployer/deploy/v3/queryDeployLog`].some((url) => url === config.url)
  ) {
    return;
  }
  const { individualCaseApp, individualCaseApps, individualCaseDeployer, individualCaseAppCode } =
    useIndividual();
  if (individualCaseApp || individualCaseApps || individualCaseDeployer) {
    config.headers['individualCase'] = 'true';
    if (!!individualCaseAppCode) {
      config.headers['individualCaseAppCode'] = individualCaseAppCode;
    }
  }
}

// 匹配url
function checkUrl(flag: string, url: string): boolean {
  const { adesignerUrl } = (useGlobalStore.getState() as any)?.config || {};
  const reflectUrl = {
    token: ['/iam/', '/athenadeployer', '/apimgmt', '/bot/'],
    routerKey: [
      'uibot',
      'atmc',
      'atdm',
      'aim',
      'aam',
      'smartdata',
      'flowengine',
      'im',
      'digiwinabi',
      'thememap',
      'themedata',
      'apimgmt',
      'deployer',
      adesignerUrl,
    ],
    branch: [adesignerUrl],
    appToken: ['iam', 'boss', 'cac', 'dmc', 'msc', 'emc', 'lmc', 'eoc', 'pmc', 'omc', 'gmc'],
  };
  return reflectUrl[flag].some((s) => url.includes(s));
}

/**
 * 处理error
 * @param error
 */
async function handleError(error) {
  const errorCode = error.response?.data?.code;

  console.error(error);

  if (errorCode === 1010001) {
    // token失效处理
    if (!tokenInvalidation) {
      tokenInvalidation = true;
      message.error(i18n.t('dj-token失效'));
      const t = setTimeout(() => {
        clearTimeout(t);
        localStorage.clear();
        sessionStorage.clear();
        location.href = '/login';
      }, 1000);
    }
    return;
  }

  if (error.response?.status === 401 || errorCode === 401) {
    localStorage.clear();
    sessionStorage.clear();
    location.href = '/login';
    return;
  }

  if ([2000100001, 2000100002, 2000100003].includes(errorCode)) {
    // 全局通过拦截接口异常码，统一处理未授权、解决方案过期等异常
    const exceptionEvent: CustomEvent = new CustomEvent(CustomEventName.EXCEPTION_EVENT, {
      detail: { code: errorCode, errorInfo: error.response?.data },
    });
    document.dispatchEvent(exceptionEvent);
    return;
  }

  if (error.response?.data?.msg) {
    message.error(error.response.data.msg);
    return;
  }

  // 图标是前端从阿里图标库的扒下的路由，不符合标准的接口格式，所以不走统一处理
  if (
    ['iconfont/iconfont-platform/iconfont.json', 'iconfont/iconfont-app/iconfont.json'].some(
      (item) => error.response?.config?.url?.includes(item)
    )
  ) {
    return;
  }

  if (error.message) {
    message.error(error.message);
    return;
  }
}
