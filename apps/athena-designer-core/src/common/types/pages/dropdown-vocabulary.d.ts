declare module DV {
  export type AnyOBJ = {
    [key: string]: any;
  };
  export type Action<T = AnyOBJ> = {
    type: import('../pages/DataCenter/pages/DropdownVocabulary/hooks/useDVModuleStateHook').DVLOADING_ENUM;
    data?: T;
  };
  export type QueryParams = {
    standard: string;
    auditStatus: any;
    condition: string;
    pageIndex: number;
    pageSize: number;
    sourcePage: string;
  };
  export type ModuleState = {
    isLoading: boolean;
    authUserJudge: boolean;
    queryParams: QueryParams;
    selectedRows: DV.RowData[];
    tableLists: RowData[];
    editModalInfo: EditModalProps;
  };
  export type ModuleContext<T = AnyOBJ> = {
    state: ModuleState;
    setDVState: import('react').Dispatch<Action<T>>;
  };
  export type ServiceConfig<T = AnyOBJ> = {
    [key: string]: AxiosConf<T>;
  };
  export type AxiosConf<T = AnyOBJ> = import('axios').AxiosRequestConfig<T>;
  export interface UseAxiosConfig<T = AnyOBJ> extends AxiosConf<T> {
    attachDVModule?: boolean;
  }
  export type LangConfig = {
    [key: SupportedLangValue]: string;
  };
  export type SubRowData = {
    uuid: string;
    code?: string;
    sort?: number;
    value?: string;
    lang?: {
      value?: LangConfig;
    };
  };
  export type RowData = {
    auditStatus?: any;
    _id?: string;
    id?: string;
    key?: string;
    code?: string;
    value?: string;
    desc?: string;
    lang?: {
      description?: LangConfig;
    };
    checked?: boolean;
    transLang?: string;
    standard?: string;
    tenantId?: string;
    createBy?: string;
    createTime?: string;
    description?: string;
    editBy?: string;
    editTime?: string;
    enumKey?: string;
    values?: SubRowData[];
    disabled?: boolean;
    tenantIdDisabled?: boolean;
    authDisabled?: boolean;
  };
  export type AgGridCellParams<T = AnyOBJ> = {
    data: T;
    node: any;
    columnApi: any;
  };
  export type AjaxPaginationData<T = AnyOBJ> = {
    curPageNum?: number;
    limit?: number;
    total?: number;
    totalPageNum?: number;
    data?: T[];
    queryResult?: T[];
  };
  export type AjaxPaginationData1<T = AnyOBJ> = {
    current?: number;
    pages: number;
    records?: T[];
    total: number;
    size: number;
  };
  export type AjaxCommonResponse<T = AnyOBJ> = {
    code?: number;
    data?: T;
    message?: string;
    msg?: string;
  };
  export type UseAxiosReturn<R = AnyOBJ, Q extends UseAxiosConfig = UseAxiosConfig> = {
    data: R;
    error?: import('axios').AxiosError;
    loading: boolean;
    fetch: (data?: AnyOBJ, extraParams?: Q) => Promise<R>;
  };
  export type TransformListFunc<I, R> = (input: I) => R;
  export type AnyFunction = (...args: any[]) => any;
  export type DebounceParams<T extends AnyFunction = AnyFunction> = {
    callback: T;
    delay?: number;
    dependencies?: readonly unknown[];
    noDelayCondition?: (prev: unknown, current: unknown) => boolean;
    excludeMounted?: boolean;
  };
  export type TAjaxRes<T extends AnyOBJ = AnyOBJ> = DV.AjaxCommonResponse<T>;
  export type EditModalProps = {
    isShow: boolean;
    opType?: import('../pages/DataCenter/pages/DropdownVocabulary/components/EditModal').EditActionType;
    info?: RowData;
    disableCode?: boolean;
    disableCheckBox?: boolean;
  };
  export type OpAction<T = AnyOBJ> = {
    type: import('src/common/types/index').CommonAction;
    id?: string | number;
    data: T;
  };
  export type LangIconProps<T> = {
    title?: string;
    data: LangConfig;
    isTextArea?: boolean;
    id?: string | number;
    classNames?: any;
    useTranslate: boolean;
    callback: (data: OpAction<T>) => void;
    syncData?: (data: LangConfig) => void;
    syncStatus?: (open: boolean) => void;
  };
  export type AsyncFunction<T = unknown> = (...args: unknown[]) => Promise<T>;
  export type UseLoadingReturn<T = unknown> = {
    fn: AsyncFunction<T>;
    loading: boolean;
  };
  type SupportedLangType = import('src/components/LangIcon/lang-tools').SupportedLang;
  export type SupportedLangValue = Record<SupportedLang, 'string'>;
  export interface CheckReqRes {
    referenced: string[];
    nonTenant: string[];
  }
  export interface TipInfoType {
    isShow: boolean;
    id: string[];
    referenced: string;
    nonTenant: string;
  }
  export type ExpandRowProps = {
    rowData: RowData;
  };
}
